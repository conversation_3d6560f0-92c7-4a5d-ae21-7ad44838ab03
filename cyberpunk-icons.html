<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyberpunk Icons - Navigation</title>
    <link rel="stylesheet" href="cyberpunk-simple.css">
    <link rel="stylesheet" href="cyberpunk-icons.css">
    <style>
        /* Demo specific styles */
        .demo-container {
            padding: 20px;
            max-width: 400px;
            margin: 0 auto;
            padding-top: 80px;
            padding-bottom: 100px;
        }
        
        .icon-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .icon-demo {
            background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .icon-demo:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 255, 255, 0.2);
        }
        
        .icon-demo h4 {
            color: var(--cyber-accent-neon);
            margin: 10px 0 5px 0;
            font-family: 'Orbitron', monospace;
            font-size: 14px;
        }
        
        .icon-demo p {
            color: var(--cyber-text-muted);
            font-size: 12px;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="app-header">
        <div class="user-info">
            <div class="user-avatar">
                <img src="./images/user.svg" class="user-avatar-icon" alt="user">
            </div>
            <div class="user-name">CyberUser_2077</div>
        </div>
        <div class="balance-info">
            <span class="balance-amount">1,337</span>
            <span class="balance-currency">монет</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="demo-container">
        <h1 style="text-align: center; color: var(--cyber-accent-neon); font-family: 'Orbitron', monospace; text-shadow: 0 0 10px var(--cyber-glow); margin-bottom: 30px;">
            🚀 CYBERPUNK ICONS
        </h1>

        <div class="status-message success">
            ✅ Крутые иконки для навигации готовы!
        </div>

        <!-- Icon Showcase -->
        <div class="icon-showcase">
            <!-- Home Icon -->
            <div class="icon-demo">
                <svg class="cyber-icon home-icon cyber-icon-variant-1" viewBox="0 0 24 24">
                    <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    <circle cx="12" cy="8" r="1" fill="currentColor"/>
                    <rect x="10" y="14" width="4" height="3" rx="0.5" fill="none" stroke="currentColor" stroke-width="1"/>
                </svg>
                <h4>ГЛАВНАЯ</h4>
                <p>Домашний экран</p>
            </div>

            <!-- Earn Icon -->
            <div class="icon-demo">
                <svg class="cyber-icon earn-icon cyber-icon-variant-1" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="9" fill="none" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M12 6v12M8 10l4-4 4 4M8 14l4 4 4-4" stroke="currentColor" stroke-width="1.5" fill="none"/>
                    <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="1"/>
                    <text x="12" y="16" text-anchor="middle" font-size="6" fill="currentColor">$</text>
                </svg>
                <h4>ЗАРАБОТОК</h4>
                <p>Получить монеты</p>
            </div>

            <!-- Friends Icon -->
            <div class="icon-demo">
                <svg class="cyber-icon friends-icon cyber-icon-variant-1" viewBox="0 0 24 24">
                    <circle cx="9" cy="7" r="3" fill="none" stroke="currentColor" stroke-width="1.2"/>
                    <circle cx="15" cy="7" r="2" fill="none" stroke="currentColor" stroke-width="1.2"/>
                    <path d="M3 21v-2a4 4 0 014-4h4a4 4 0 014 4v2" stroke="currentColor" stroke-width="1.2" fill="none"/>
                    <path d="M16 21v-2a4 4 0 013-3.87" stroke="currentColor" stroke-width="1.2" fill="none"/>
                    <circle cx="6" cy="10" r="1" fill="currentColor"/>
                    <circle cx="18" cy="10" r="1" fill="currentColor"/>
                </svg>
                <h4>ДРУЗЬЯ</h4>
                <p>Рефералы</p>
            </div>
        </div>

        <!-- Alternative Icon Styles -->
        <h2 style="color: var(--cyber-accent-pink); font-family: 'Orbitron', monospace; text-align: center; margin: 40px 0 20px 0;">
            🎨 АЛЬТЕРНАТИВНЫЕ СТИЛИ
        </h2>

        <div class="icon-showcase">
            <!-- Futuristic Home -->
            <div class="icon-demo">
                <svg class="cyber-icon cyber-icon-variant-2" viewBox="0 0 24 24">
                    <polygon points="12,2 22,12 20,12 20,22 15,22 15,16 9,16 9,22 4,22 4,12 2,12" fill="currentColor"/>
                    <rect x="10" y="14" width="4" height="2" fill="var(--cyber-bg-primary)"/>
                    <circle cx="12" cy="8" r="1.5" fill="var(--cyber-accent-neon)"/>
                </svg>
                <h4>CYBER HOME</h4>
                <p>Футуристический дом</p>
            </div>

            <!-- Digital Money -->
            <div class="icon-demo">
                <svg class="cyber-icon cyber-icon-variant-2" viewBox="0 0 24 24">
                    <rect x="2" y="6" width="20" height="12" rx="3" fill="currentColor"/>
                    <rect x="4" y="8" width="16" height="8" rx="2" fill="var(--cyber-bg-primary)"/>
                    <circle cx="12" cy="12" r="3" fill="currentColor"/>
                    <text x="12" y="15" text-anchor="middle" font-size="8" fill="var(--cyber-bg-primary)" font-weight="bold">$</text>
                    <rect x="6" y="10" width="2" height="1" fill="var(--cyber-accent-neon)"/>
                    <rect x="16" y="10" width="2" height="1" fill="var(--cyber-accent-neon)"/>
                </svg>
                <h4>DIGITAL $</h4>
                <p>Цифровые деньги</p>
            </div>

            <!-- Network Friends -->
            <div class="icon-demo">
                <svg class="cyber-icon cyber-icon-variant-2" viewBox="0 0 24 24">
                    <circle cx="8" cy="8" r="3" fill="currentColor"/>
                    <circle cx="16" cy="8" r="2.5" fill="currentColor"/>
                    <circle cx="12" cy="16" r="2" fill="currentColor"/>
                    <line x1="8" y1="11" x2="12" y2="14" stroke="var(--cyber-accent-neon)" stroke-width="2"/>
                    <line x1="16" y1="11" x2="12" y2="14" stroke="var(--cyber-accent-neon)" stroke-width="2"/>
                    <circle cx="8" cy="8" r="1" fill="var(--cyber-bg-primary)"/>
                    <circle cx="16" cy="8" r="1" fill="var(--cyber-bg-primary)"/>
                    <circle cx="12" cy="16" r="0.8" fill="var(--cyber-bg-primary)"/>
                </svg>
                <h4>NETWORK</h4>
                <p>Сеть друзей</p>
            </div>
        </div>

        <!-- Glitch Effect Icons -->
        <h2 style="color: var(--cyber-accent-purple); font-family: 'Orbitron', monospace; text-align: center; margin: 40px 0 20px 0;">
            ⚡ GLITCH ЭФФЕКТЫ
        </h2>

        <div class="icon-showcase">
            <!-- Glitch Home -->
            <div class="icon-demo">
                <svg class="cyber-icon cyber-icon-variant-3" viewBox="0 0 24 24">
                    <path d="M3 12l9-9 9 9M5 10v10h4v-6h6v6h4V10" stroke-dasharray="3,2"/>
                    <rect x="9" y="14" width="6" height="4" fill="none" stroke="currentColor" stroke-dasharray="2,1"/>
                </svg>
                <h4>GLITCH HOME</h4>
                <p>Эффект помех</p>
            </div>

            <!-- Matrix Money -->
            <div class="icon-demo">
                <svg class="cyber-icon cyber-icon-variant-3" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="8" stroke-dasharray="4,2"/>
                    <path d="M8 12h8M12 8v8" stroke-dasharray="2,1"/>
                    <text x="12" y="16" text-anchor="middle" font-size="6" fill="currentColor">₿</text>
                </svg>
                <h4>MATRIX $</h4>
                <p>Криптовалюта</p>
            </div>

            <!-- Cyber Friends -->
            <div class="icon-demo">
                <svg class="cyber-icon cyber-icon-variant-3" viewBox="0 0 24 24">
                    <circle cx="9" cy="7" r="3" stroke-dasharray="3,1"/>
                    <circle cx="15" cy="7" r="2" stroke-dasharray="2,1"/>
                    <path d="M3 21v-2a4 4 0 014-4h4a4 4 0 014 4v2" stroke-dasharray="4,2"/>
                    <path d="M16 21v-2a4 4 0 013-3.87" stroke-dasharray="3,1"/>
                </svg>
                <h4>CYBER TEAM</h4>
                <p>Кибер команда</p>
            </div>
        </div>

        <!-- Instructions -->
        <div class="friends-block" style="margin-top: 40px;">
            <h3>🛠 Как использовать иконки</h3>
            <p><strong>1.</strong> Скопируй SVG код нужной иконки</p>
            <p><strong>2.</strong> Замени иконки в навигации</p>
            <p><strong>3.</strong> Добавь класс <code>cyber-icon</code></p>
            <p><strong>4.</strong> Подключи <code>cyberpunk-icons.css</code></p>
            
            <button class="action-button purple-button" onclick="copyIconCode()">
                📋 Скопировать код иконок
            </button>
        </div>
    </div>

    <!-- Navigation with New Icons -->
    <nav class="app-nav">
        <button class="nav-button active" id="nav-home">
            <svg class="cyber-icon home-icon" viewBox="0 0 24 24">
                <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" 
                      stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="8" r="1" fill="currentColor"/>
            </svg>
            <span>ГЛАВНАЯ</span>
        </button>
        
        <button class="nav-button" id="nav-earn">
            <svg class="cyber-icon earn-icon" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="9" fill="none" stroke="currentColor" stroke-width="1.5"/>
                <path d="M12 6v12M8 10l4-4 4 4M8 14l4 4 4-4" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="1"/>
                <text x="12" y="16" text-anchor="middle" font-size="6" fill="currentColor">$</text>
            </svg>
            <span>ЗАРАБОТОК</span>
        </button>
        
        <button class="nav-button" id="nav-friends">
            <svg class="cyber-icon friends-icon" viewBox="0 0 24 24">
                <circle cx="9" cy="7" r="3" fill="none" stroke="currentColor" stroke-width="1.2"/>
                <circle cx="15" cy="7" r="2" fill="none" stroke="currentColor" stroke-width="1.2"/>
                <path d="M3 21v-2a4 4 0 014-4h4a4 4 0 014 4v2" stroke="currentColor" stroke-width="1.2" fill="none"/>
                <path d="M16 21v-2a4 4 0 013-3.87" stroke="currentColor" stroke-width="1.2" fill="none"/>
                <circle cx="6" cy="10" r="1" fill="currentColor"/>
                <circle cx="18" cy="10" r="1" fill="currentColor"/>
            </svg>
            <span>ДРУЗЬЯ</span>
        </button>
    </nav>

    <script>
        // Navigation functionality
        document.querySelectorAll('.nav-button').forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.nav-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Add glitch effect
                this.classList.add('glitch-effect');
                setTimeout(() => {
                    this.classList.remove('glitch-effect');
                }, 300);
            });
        });

        // Copy icon code function
        function copyIconCode() {
            const iconCode = `
<!-- Cyberpunk Navigation Icons -->
<svg class="cyber-icon home-icon" viewBox="0 0 24 24">
    <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" 
          stroke="currentColor" stroke-width="1.5" fill="none"/>
    <circle cx="12" cy="8" r="1" fill="currentColor"/>
</svg>

<svg class="cyber-icon earn-icon" viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="9" fill="none" stroke="currentColor" stroke-width="1.5"/>
    <path d="M12 6v12M8 10l4-4 4 4M8 14l4 4 4-4" stroke="currentColor" stroke-width="1.5" fill="none"/>
    <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="1"/>
    <text x="12" y="16" text-anchor="middle" font-size="6" fill="currentColor">$</text>
</svg>

<svg class="cyber-icon friends-icon" viewBox="0 0 24 24">
    <circle cx="9" cy="7" r="3" fill="none" stroke="currentColor" stroke-width="1.2"/>
    <circle cx="15" cy="7" r="2" fill="none" stroke="currentColor" stroke-width="1.2"/>
    <path d="M3 21v-2a4 4 0 014-4h4a4 4 0 014 4v2" stroke="currentColor" stroke-width="1.2" fill="none"/>
    <path d="M16 21v-2a4 4 0 013-3.87" stroke="currentColor" stroke-width="1.2" fill="none"/>
    <circle cx="6" cy="10" r="1" fill="currentColor"/>
    <circle cx="18" cy="10" r="1" fill="currentColor"/>
</svg>
            `;
            
            navigator.clipboard.writeText(iconCode).then(() => {
                // Show success message
                const messageEl = document.createElement('div');
                messageEl.className = 'status-message success';
                messageEl.textContent = '✅ Код иконок скопирован в буфер обмена!';
                messageEl.style.position = 'fixed';
                messageEl.style.top = '80px';
                messageEl.style.left = '15px';
                messageEl.style.right = '15px';
                messageEl.style.zIndex = '9999';
                
                document.body.appendChild(messageEl);
                
                setTimeout(() => {
                    messageEl.style.opacity = '0';
                    setTimeout(() => {
                        document.body.removeChild(messageEl);
                    }, 300);
                }, 3000);
            });
        }

        console.log('🚀 Cyberpunk Icons Demo loaded!');
    </script>
</body>
</html>
