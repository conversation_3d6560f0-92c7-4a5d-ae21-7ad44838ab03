<?php
/**
 * Тест минимальных сумм для всех валют
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "💰 ТЕСТ МИНИМАЛЬНЫХ СУММ ДЛЯ ВСЕХ ВАЛЮТ\n";
echo str_repeat("=", 60) . "\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Список валют для проверки
$currencies = [
    'usdttrc20' => 'USDT (TRC20)',
    'usdterc20' => 'USDT (ERC20)', 
    'btc' => 'Bitcoin (BTC)',
    'eth' => 'Ethereum (ETH)',
    'trx' => 'TRON (TRX)',
    'bnb' => 'Binance Coin (BNB)',
    'ltc' => 'Litecoin (LTC)',
    'bch' => 'Bitcoin Cash (BCH)',
    'xrp' => 'Ripple (XRP)',
    'ada' => 'Cardano (ADA)',
    'dot' => 'Polkadot (DOT)',
    'doge' => 'Dogecoin (DOGE)',
    'matic' => 'Polygon (MATIC)',
    'sol' => 'Solana (SOL)',
    'avax' => 'Avalanche (AVAX)',
    'usdc' => 'USD Coin (USDC)',
    'dai' => 'Dai (DAI)',
    'link' => 'Chainlink (LINK)',
    'uni' => 'Uniswap (UNI)',
    'atom' => 'Cosmos (ATOM)'
];

echo "🔍 Проверяем минимальные суммы для " . count($currencies) . " валют:\n\n";

$results = [];
$successCount = 0;
$failCount = 0;

foreach ($currencies as $currency => $name) {
    echo "📊 {$name} ({$currency}):\n";
    
    // Получаем минимальную сумму
    $minAmount = $api->getMinWithdrawalAmount($currency);
    
    if ($minAmount !== null && $minAmount !== false) {
        echo "   ✅ Минимум: {$minAmount}\n";
        $results[$currency] = [
            'name' => $name,
            'min_amount' => $minAmount,
            'status' => 'success'
        ];
        $successCount++;
    } else {
        echo "   ❌ Не удалось получить минимум\n";
        $results[$currency] = [
            'name' => $name,
            'min_amount' => null,
            'status' => 'failed'
        ];
        $failCount++;
    }
    
    // Небольшая пауза между запросами
    usleep(200000); // 0.2 секунды
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "📈 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ\n";
echo str_repeat("=", 60) . "\n\n";

echo "📊 Статистика:\n";
echo "✅ Успешно получено: {$successCount}\n";
echo "❌ Ошибок: {$failCount}\n";
echo "📋 Всего проверено: " . count($currencies) . "\n\n";

echo "💰 МИНИМАЛЬНЫЕ СУММЫ ДЛЯ ВЫВОДА:\n\n";

// Сортируем результаты по статусу (успешные сначала)
$successResults = array_filter($results, function($r) { return $r['status'] === 'success'; });
$failedResults = array_filter($results, function($r) { return $r['status'] === 'failed'; });

// Показываем успешные результаты
if (!empty($successResults)) {
    echo "✅ ДОСТУПНЫЕ ВАЛЮТЫ:\n";
    foreach ($successResults as $currency => $data) {
        $minAmount = $data['min_amount'];
        $name = $data['name'];
        echo sprintf("%-25s %-15s мин. %s\n", $name, "({$currency})", $minAmount);
    }
    echo "\n";
}

// Показываем неудачные результаты
if (!empty($failedResults)) {
    echo "❌ ВАЛЮТЫ БЕЗ ДАННЫХ:\n";
    foreach ($failedResults as $currency => $data) {
        $name = $data['name'];
        echo sprintf("%-25s %-15s нет данных\n", $name, "({$currency})");
    }
    echo "\n";
}

echo str_repeat("-", 60) . "\n";
echo "🔧 РЕКОМЕНДАЦИИ ДЛЯ ОБНОВЛЕНИЯ КОДА:\n\n";

if (!empty($successResults)) {
    echo "📝 Обновите массив минимальных сумм в коде:\n\n";
    echo "```php\n";
    echo "\$knownMinimums = [\n";
    
    foreach ($successResults as $currency => $data) {
        $minAmount = $data['min_amount'];
        echo "    '{$currency}' => {$minAmount},\n";
    }
    
    echo "];\n";
    echo "```\n\n";
    
    echo "📱 Обновите интерфейс (HTML):\n\n";
    echo "```html\n";
    foreach ($successResults as $currency => $data) {
        $name = $data['name'];
        $minAmount = $data['min_amount'];
        echo "<option value=\"{$currency}\">{$name} - мин. {$minAmount}</option>\n";
    }
    echo "```\n\n";
    
    echo "🔄 Обновите JavaScript:\n\n";
    echo "```javascript\n";
    echo "const minAmounts = {\n";
    foreach ($successResults as $currency => $data) {
        $minAmount = $data['min_amount'];
        echo "    '{$currency}': '{$minAmount}',\n";
    }
    echo "};\n";
    echo "```\n\n";
}

echo "💡 ВАЖНЫЕ ЗАМЕЧАНИЯ:\n";
echo "1. Минимальные суммы могут изменяться NOWPayments\n";
echo "2. Рекомендуется периодически обновлять данные\n";
echo "3. Для продакшена лучше получать минимумы динамически\n";
echo "4. Некоторые валюты могут быть недоступны для выплат\n\n";

echo "🎯 СЛЕДУЮЩИЕ ШАГИ:\n";
echo "1. Обновить код с актуальными минимальными суммами\n";
echo "2. Обновить интерфейс с новыми данными\n";
echo "3. Протестировать выплаты с обновленными лимитами\n";
echo "4. Добавить автоматическое обновление минимумов\n\n";

echo str_repeat("=", 60) . "\n";
echo "🏁 Тестирование минимальных сумм завершено\n";

// Сохраняем результаты в JSON файл для дальнейшего использования
$jsonResults = [
    'timestamp' => date('Y-m-d H:i:s'),
    'total_currencies' => count($currencies),
    'success_count' => $successCount,
    'fail_count' => $failCount,
    'results' => $results
];

file_put_contents('min_amounts_results.json', json_encode($jsonResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "\n💾 Результаты сохранены в файл: min_amounts_results.json\n";
?>
