<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>Applanza App - Cyberpunk Edition</title>
    <link rel="stylesheet" href="cyberpunk-simple.css">
    <link rel="stylesheet" href="cyberpunk-icons.css">
    <!-- Скрипты подключаются в конце body -->
</head>
<body>

    <!-- Контейнер приложения -->
    <div class="app-container">

        <!-- Шапка -->
        <header class="app-header">
            <div class="user-info">
                <!-- Аватар пока плейсхолдер, можно заменить на img или div с фоном -->
                <div class="user-avatar">
                    <img src="./images/user.svg" class="user-avatar-icon" alt="user">
                </div>
                <div class="user-name" id="user-name">Загрузка...</div>
            </div>
            <div class="balance-info clickable-balance" id="header-balance-info" title="Перейти к выводу средств">
                <!-- Иконка баланса из спрайта -->
                <svg class="balance-icon"></svg> <!-- Или #icon-dollar, #icon-money -->
                <span class="balance-amount" id="balance-amount">0</span>
                <span class="balance-currency">монет</span> <!-- Оставим "монет" или можно ₽ -->
            </div>
        </header>

        <!-- Основной контент (Задания) -->
        <main class="app-section active-section" id="main-content"> <!-- Добавлен класс active-section -->
            <div id="status-message" class="status-message">Ожидание инициализации...</div>
            <h2 data-section="tasks">Задания</h2>
            <button id="openLinkButton" class="action-button purple-button">
                Открыть ссылку
            </button>
            <button id="watchVideoButton" class="action-button blue-button">
                Смотреть видео
            </button>
            <button id="openAdButton" class="action-button orange-button">
                Открыть рекламу
            </button>
             <!-- <button id="paid-survey-button" class="action-button secondary-action" disabled>
                 <svg class="button-icon"><use href="images/sprite.svg#icon-money"></use></svg>
                 Пройти опрос
            </button> -->
        </main>

        <!-- Секция "Заработок" (Вывод средств - заглушка) -->
        <section class="app-section earn-section page-hidden" id="earn-section"> <!-- Добавлен page-hidden -->
            <h2>Вывод средств</h2>
            <div class="earn-block">
                <h3>Ваш баланс</h3>
                <div class="current-balance-display">
                    <svg class="balance-icon"><use href="images/sprite.svg#icon-ruble"></use></svg>
                    <span class="balance-amount" id="earn-balance-amount">0</span>
                    <span class="balance-currency">монет</span>
                </div>
                <p class="hint">Доступно для вывода: <span id="available-withdrawal">0</span> монет.</p>
            </div>
             <div class="earn-block">
                <h3>💰 Калькулятор вывода</h3>

                <div class="calculator-header">
                    <p class="calculator-subtitle">Курс: 1 монета = $0.001</p>
                    <div class="balance-display">
                        <span class="balance-label">Ваш баланс:</span>
                        <span class="balance-amount" id="calc-balance">0 монет</span>
                    </div>
                </div>

                <!-- Поле ввода суммы -->
                <div class="amount-input-section">
                    <label for="calc-amount">Сумма для вывода:</label>
                    <div class="input-group">
                        <input type="number" id="calc-amount" placeholder="Введите количество монет" min="0" step="1">
                        <span class="input-suffix">монет</span>
                    </div>
                    <div class="amount-info">
                        <span id="dollar-equivalent">= $0.000</span>
                        <span id="balance-check" class="balance-status">Введите сумму</span>
                    </div>
                </div>

                <!-- Табы валют -->
                <div class="currency-tabs-container">
                    <div class="currency-tabs-header">
                        <button class="currency-tab active" data-currency="eth">
                            <span class="tab-icon">⭐</span>
                            <span class="tab-name">Ethereum</span>
                            <span class="tab-symbol">ETH</span>
                        </button>
                        <button class="currency-tab" data-currency="btc">
                            <span class="tab-icon">₿</span>
                            <span class="tab-name">Bitcoin</span>
                            <span class="tab-symbol">BTC</span>
                        </button>
                        <button class="currency-tab" data-currency="usdttrc20">
                            <span class="tab-icon">💲</span>
                            <span class="tab-name">USDT</span>
                            <span class="tab-symbol">TRC20</span>
                        </button>
                        <button class="currency-tab" data-currency="trx">
                            <span class="tab-icon">🔺</span>
                            <span class="tab-name">TRON</span>
                            <span class="tab-symbol">TRX</span>
                        </button>
                    </div>

                    <!-- Контент выбранной валюты -->
                    <div class="currency-content">
                        <div class="currency-info-card" id="currency-info">
                            <div class="currency-main-info">
                                <div class="currency-title">
                                    <span class="currency-icon">⭐</span>
                                    <span class="currency-full-name">Ethereum (ETH)</span>
                                    <span class="currency-badge status-best">Лучший выбор</span>
                                </div>
                                <div class="currency-requirements">
                                    <div class="requirement-item">
                                        <span class="requirement-label">Минимум:</span>
                                        <span class="requirement-value">1,000 монет ($1.00)</span>
                                    </div>
                                    <div class="requirement-item">
                                        <span class="requirement-label">Сетевая комиссия:</span>
                                        <span class="requirement-value fee-amount">$0.53</span>
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-results" id="calculation-results">
                                <div class="calculation-row">
                                    <span class="calc-label">Сумма к выводу:</span>
                                    <span class="calc-value" id="withdrawal-amount-display">-</span>
                                </div>
                                <div class="calculation-row">
                                    <span class="calc-label">Комиссия сети:</span>
                                    <span class="calc-value fee-value" id="fee-amount-display">-</span>
                                </div>
                                <div class="calculation-row total-row">
                                    <span class="calc-label">Вы получите:</span>
                                    <span class="calc-value total-value" id="final-amount-display">-</span>
                                </div>
                                <div class="efficiency-row">
                                    <span class="efficiency-label">Эффективность:</span>
                                    <span class="efficiency-value" id="efficiency-display">-</span>
                                </div>
                            </div>

                            <div class="action-status-card" id="action-status">
                                <div class="status-icon">💡</div>
                                <div class="status-text">Введите сумму для расчета</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

             <div class="earn-block">
                <h3>Заявка на вывод</h3>
                <p class="hint">Выберите валюту из калькулятора выше и укажите адрес кошелька.</p>
                 <div class="withdrawal-form">
                     <label for="crypto-currency">Выбранная криптовалюта:</label>
                     <select id="crypto-currency" class="crypto-select">
                         <option value="usdttrc20">USDT (TRC20)</option>
                         <option value="btc">Bitcoin (BTC)</option>
                         <option value="eth">Ethereum (ETH)</option>
                         <option value="trx">TRON (TRX)</option>
                     </select>

                     <label for="withdrawal-amount">Сумма для вывода (монеты):</label>
                     <input type="number" id="withdrawal-amount" placeholder="Введите сумму" min="0" step="1" readonly>

                     <label for="crypto-amount">Сумма к получению:</label>
                     <input type="text" id="crypto-amount" class="crypto-amount-field" placeholder="Будет рассчитано автоматически" readonly>

                     <label for="withdrawal-address">Адрес кошелька:</label>
                     <input type="text" id="withdrawal-address" placeholder="Введите адрес кошелька">

                     <button id="request-withdrawal-button" class="action-button primary-action" disabled>
                         Запросить вывод
                     </button>
                 </div>
                 <p class="hint error-message" id="withdrawal-error" style="display: none;"></p>
                 <p class="hint"><strong>Важно:</strong> Убедитесь, что адрес кошелька указан корректно. Средства будут отправлены на указанный адрес и не могут быть возвращены в случае ошибки.</p>
            </div>
            <div class="earn-block">
                <h3>История выплат</h3>
                <div id="withdrawal-history" class="withdrawal-history">
                    <div class="placeholder-list">Загрузка истории выплат...</div>
                </div>
            </div>
        </section>

        <!-- Секция "Друзья" -->
        <section class="app-section friends-section page-hidden" id="friends-section"> <!-- Добавлен page-hidden -->
            <h2>Друзья и Приглашения</h2>
            <div class="friends-block">
                <h3>Поделиться приложением</h3>
                <p>Расскажи друзьям об этом приложении!</p>
                <button id="share-app-button" class="action-button secondary-action">
                    Поделиться
                </button>
            </div>
            <div class="friends-block">
                <h3>Пригласить друга (Ваша ссылка)</h3>
                <p>Поделитесь ссылкой. Вы будете получать 10% от заработка друзей, пришедших по ней!</p>
                <div class="referral-link-area">
                    <input type="text" id="referral-link-input" value="Генерация ссылки..." readonly>
                    <button id="copy-referral-button" class="copy-button" title="Копировать" disabled>
                        <svg class="button-icon small-icon"><use href="images/sprite.svg#icon-link"></use></svg> <!-- Маленькая иконка копирования -->
                    </button>
                </div>
            </div>
            <div class="friends-block">
                <h3>Статистика рефералов</h3>
                <div class="referral-stats">
                    <div class="stat-item">
                        <div class="stat-label">Всего рефералов:</div>
                        <div class="stat-value" id="referrals-count">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Заработано на рефералах:</div>
                        <div class="stat-value" id="referral-earnings">0</div>
                    </div>
                </div>
                <div id="referrals-list" class="referrals-list">
                    <p class="hint">У вас пока нет рефералов. Пригласите друзей!</p>
                </div>
                <button id="refresh-stats-button" class="action-button secondary-action">
                    Обновить статистику
                </button>
            </div>
            <div class="friends-block">
                <h3>Подписки</h3>
                <div id="subscriptions-list" class="referrals-list">
                    <p class="hint">Загрузка информации...</p>
                </div>
            </div>
        </section>

        <!-- Нижняя навигация с кибер-панк иконками -->
        <nav class="app-nav">
            <button class="nav-button active" id="nav-home">
                <svg class="cyber-icon home-icon" viewBox="0 0 24 24">
                    <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                          stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="8" r="1" fill="currentColor"/>
                </svg>
                <span class="nav-text" data-section="tasks">ГЛАВНАЯ</span>
            </button>
            <button class="nav-button" id="nav-earn">
                <svg class="cyber-icon earn-icon" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="9" fill="none" stroke="currentColor" stroke-width="1.5"/>
                    <path d="M12 6v12M8 10l4-4 4 4M8 14l4 4 4-4" stroke="currentColor" stroke-width="1.5" fill="none"/>
                    <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="1"/>
                    <text x="12" y="16" text-anchor="middle" font-size="6" fill="currentColor">$</text>
                </svg>
                <span class="nav-text" data-section="earnings">ЗАРАБОТОК</span>
            </button>
            <button class="nav-button" id="nav-friends">
                <svg class="cyber-icon friends-icon" viewBox="0 0 24 24">
                    <circle cx="9" cy="7" r="3" fill="none" stroke="currentColor" stroke-width="1.2"/>
                    <circle cx="15" cy="7" r="2" fill="none" stroke="currentColor" stroke-width="1.2"/>
                    <path d="M3 21v-2a4 4 0 014-4h4a4 4 0 014 4v2" stroke="currentColor" stroke-width="1.2" fill="none"/>
                    <path d="M16 21v-2a4 4 0 013-3.87" stroke="currentColor" stroke-width="1.2" fill="none"/>
                    <circle cx="6" cy="10" r="1" fill="currentColor"/>
                    <circle cx="18" cy="10" r="1" fill="currentColor"/>
                </svg>
                <span class="nav-text" data-section="referrals">ДРУЗЬЯ</span>
            </button>
        </nav>

    </div> <!-- /app-container -->

    <!-- Подключение скриптов в конце -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
    <script src="js/localization.js"></script>
    <script src="cyberpunk-effects.js"></script>
    <script src="cyberpunk-navigation-fix.js"></script>
    <script src="main.js"></script>

</body>
</html>