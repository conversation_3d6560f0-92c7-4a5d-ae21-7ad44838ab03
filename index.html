﻿<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>Applanza App - Cyberpunk Edition</title>
    <link rel="stylesheet" href="cyberpunk-styles.css">
    <!-- Скрипты подключаются в конце body -->
</head>
<body>

    <!-- Cyberpunk Coins Background -->
    <div class="cyberpunk-coins-bg"></div>

    <!-- Glitch Lines -->
    <div class="glitch-line"></div>
    <div class="glitch-line"></div>
    <div class="glitch-line"></div>

    <!-- Контейнер приложения -->
    <div class="app-container">

        <!-- Шапка -->
        <header class="app-header">
            <div class="user-info">
                <!-- Аватар пока плейсхолдер, можно заменить на img или div с фоном -->
                <div class="user-avatar">
                    <img src="./images/user.svg" class="user-avatar-icon" alt="user">
                </div>
                <div class="user-name" id="user-name">Загрузка...</div>
            </div>
            <div class="balance-info clickable-balance" id="header-balance-info" title="Баланс">
                <span class="balance-amount" id="balance-amount">1,337</span>
                <span class="balance-currency">монет</span>
            </div>
        </header>

        <!-- Основной контент (Задания) -->
        <main class="app-section active-section" id="main-content"> <!-- Добавлен класс active-section -->
            <div id="status-message" class="status-message">Ожидание инициализации...</div>
            <h2 data-section="tasks">Задания</h2>
            <button id="openLinkButton" class="action-button purple-button">
                Открыть ссылку
            </button>
            <button id="watchVideoButton" class="action-button blue-button">
                Смотреть видео
            </button>
            <button id="openAdButton" class="action-button orange-button">
                Открыть рекламу
            </button>

             <!-- <button id="paid-survey-button" class="action-button secondary-action" disabled>
                 <svg class="button-icon"><use href="images/sprite.svg#icon-money"></use></svg>
                 Пройти опрос
            </button> -->
        </main>

        <!-- Секция "Заработок" (Вывод средств - заглушка) -->
        <section class="app-section earn-section page-hidden" id="earn-section"> <!-- Добавлен page-hidden -->
            <h2>Вывод средств</h2>
            <div class="earn-block">
                <h3>💎 Ваш баланс</h3>
                <p>Текущий баланс вашего аккаунта и доступные для вывода средства.</p>
                <div class="current-balance-display" style="text-align: center; padding: 20px; background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 15px; margin: 15px 0;">
                    <span class="balance-amount" id="earn-balance-amount" style="font-size: 28px; color: var(--cyber-accent-neon); font-family: 'Orbitron', monospace; text-shadow: 0 0 10px var(--cyber-glow);">1,337</span>
                    <span class="balance-currency" style="font-size: 18px; color: var(--cyber-text-primary); margin-left: 8px;">монет</span>
                </div>
                <p class="hint">Доступно для вывода: <span id="available-withdrawal" style="color: var(--cyber-accent-neon); font-weight: bold;">1,337</span> монет.</p>
            </div>
             <div class="earn-block">
                <h3 id="calculator-title">💰 Калькулятор вывода</h3>
                <p>Рассчитайте сумму для вывода в различных криптовалютах</p>

                <div class="calculator-header">
                    <p class="calculator-subtitle">Курс: 1 монета = $0.001</p>
                    <div class="balance-display">
                        <span class="balance-label">Ваш баланс:</span>
                        <span class="balance-amount" id="calc-balance">1,337 монет</span>
                    </div>
                </div>

                <!-- Поле ввода суммы -->
                <div class="amount-input-section">
                    <label for="calc-amount">Сумма для вывода:</label>
                    <div class="input-group">
                        <input type="number" id="calc-amount" placeholder="Введите количество монет" min="0" step="1">
                        <span class="input-suffix">монет</span>
                    </div>
                    <div class="amount-info">
                        <span id="dollar-equivalent">= $0.000</span>
                        <span id="balance-check" class="balance-status">Введите сумму</span>
                    </div>
                </div>

                <!-- Табы валют -->
                <div class="currency-tabs-container">
                    <div class="currency-tabs-header">
                        <button class="currency-tab active" data-currency="eth">
                            <span class="tab-icon">⭐</span>
                            <span class="tab-name">Ethereum</span>
                            <span class="tab-symbol">ETH</span>
                        </button>
                        <button class="currency-tab" data-currency="btc">
                            <span class="tab-icon">₿</span>
                            <span class="tab-name">Bitcoin</span>
                            <span class="tab-symbol">BTC</span>
                        </button>
                        <button class="currency-tab" data-currency="usdttrc20">
                            <span class="tab-icon">💲</span>
                            <span class="tab-name">USDT</span>
                            <span class="tab-symbol">TRC20</span>
                        </button>
                        <button class="currency-tab" data-currency="trx">
                            <span class="tab-icon">🔺</span>
                            <span class="tab-name">TRON</span>
                            <span class="tab-symbol">TRX</span>
                        </button>
                    </div>

                    <!-- Контент выбранной валюты -->
                    <div class="currency-content">
                        <div class="currency-info-card" id="currency-info">
                            <div class="currency-main-info">
                                <div class="currency-title">
                                    <span class="currency-icon">⭐</span>
                                    <span class="currency-full-name">Ethereum (ETH)</span>
                                    <span class="currency-badge status-best">Лучший выбор</span>
                                </div>
                                <div class="currency-requirements">
                                    <div class="requirement-item">
                                        <span class="requirement-label">Минимум:</span>
                                        <span class="requirement-value">1,000 монет ($1.00)</span>
                                    </div>
                                    <div class="requirement-item">
                                        <span class="requirement-label">Сетевая комиссия:</span>
                                        <span class="requirement-value fee-amount">$0.53</span>
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-results" id="calculation-results">
                                <div class="calculation-row">
                                    <span class="calc-label">Сумма к выводу:</span>
                                    <span class="calc-value" id="withdrawal-amount-display">-</span>
                                </div>
                                <div class="calculation-row">
                                    <span class="calc-label">Комиссия сети:</span>
                                    <span class="calc-value fee-value" id="fee-amount-display">-</span>
                                </div>
                                <div class="calculation-row total-row">
                                    <span class="calc-label">Вы получите:</span>
                                    <span class="calc-value total-value" id="final-amount-display">-</span>
                                </div>
                                <div class="efficiency-row">
                                    <span class="efficiency-label">Эффективность:</span>
                                    <span class="efficiency-value" id="efficiency-display">-</span>
                                </div>
                            </div>

                            <div class="action-status-card" id="action-status">
                                <div class="status-icon">💡</div>
                                <div class="status-text">Введите сумму для расчета</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="earn-block">
                <h3 id="withdrawal-title">💸 Вывод средств</h3>
                <p data-section="withdrawal_desc">Выберите валюту из калькулятора выше и укажите адрес кошелька для получения средств.</p>
                 <div class="withdrawal-form">
                     <label for="crypto-currency">Выбранная криптовалюта:</label>
                     <select id="crypto-currency" class="crypto-select">
                         <option value="usdttrc20">USDT (TRC20)</option>
                         <option value="btc">Bitcoin (BTC)</option>
                         <option value="eth">Ethereum (ETH)</option>
                         <option value="trx">TRON (TRX)</option>
                     </select>

                     <label for="withdrawal-amount">Сумма для вывода (монеты):</label>
                     <input type="number" id="withdrawal-amount" placeholder="Введите сумму" min="0" step="1">

                     <label for="crypto-amount">Сумма к получению:</label>
                     <input type="text" id="crypto-amount" class="crypto-amount-field" placeholder="Будет рассчитано автоматически" readonly>

                     <label for="withdrawal-address">Адрес кошелька:</label>
                     <input type="text" id="withdrawal-address" placeholder="Введите адрес кошелька">

                     <button id="request-withdrawal-button" class="action-button primary-action" disabled>
                         Запросить вывод
                     </button>
                 </div>
                 <p class="hint error-message" id="withdrawal-error" style="display: none;"></p>
                 <p class="hint"><strong>Важно:</strong> Убедитесь, что адрес кошелька указан корректно. Средства будут отправлены на указанный адрес и не могут быть возвращены в случае ошибки.</p>
            </div>

             <div class="earn-block">
                <h3 data-section="withdrawal_history">📊 История выплат</h3>
                <p data-section="withdrawal_history_desc">Здесь отображается история всех ваших выплат и их статусы.</p>
                <div id="withdrawal-history" class="withdrawal-history">
                    <div class="placeholder-list" style="background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 10px; padding: 20px; text-align: center; color: var(--cyber-text-secondary);">
                        📋 История выплат пуста<br>
                        <small style="opacity: 0.7;">Сделайте первый вывод средств</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- Секция "Друзья" -->
        <section class="app-section friends-section page-hidden" id="friends-section"> <!-- Добавлен page-hidden -->
            <h2 data-section="friends_title">Друзья и Приглашения</h2>
            <div class="friends-block">
                <h3 data-section="share_app">📱 Поделиться приложением</h3>
                <p data-section="share_app_desc">Расскажите друзьям об этом крутом кибер-панк приложении и зарабатывайте вместе!</p>
                <button id="share-app-button" class="action-button purple-button">
                    🚀 Поделиться
                </button>
            </div>
            <div class="friends-block">
                <h3 data-section="invite_friend">🔗 Пригласить друга</h3>
                <p data-section="invite_friend_desc">Поделитесь своей уникальной ссылкой и получайте 10% от заработка каждого приглашенного друга!</p>
                <div class="referral-link-area">
                    <input type="text" id="referral-link-input" value="Генерация ссылки..." readonly>
                    <button id="copy-referral-button" class="copy-button" title="Копировать" disabled>
                        <svg class="button-icon small-icon"><use href="images/sprite.svg#icon-link"></use></svg> <!-- Маленькая иконка копирования -->
                    </button>
                </div>
            </div>
            <div class="friends-block">
                <h3 data-section="referral_stats">📈 Статистика рефералов</h3>
                <p data-section="referral_stats_desc">Отслеживайте свои успехи в привлечении новых пользователей.</p>
                <div class="referral-stats">
                    <div class="stat-item">
                        <div class="stat-label">Всего рефералов:</div>
                        <div class="stat-value" id="referrals-count">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Заработано на рефералах:</div>
                        <div class="stat-value" id="referral-earnings">0 монет</div>
                    </div>
                </div>
                <div id="referrals-list" class="referrals-list">
                    <div style="background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 10px; padding: 20px; text-align: center; color: var(--cyber-text-secondary);">
                        👥 У вас пока нет рефералов<br>
                        <small style="opacity: 0.7;">Пригласите друзей и начните зарабатывать!</small>
                    </div>
                </div>
                <button id="refresh-stats-button" class="action-button blue-button">
                    🔄 Обновить статистику
                </button>
            </div>
            <div class="friends-block">
                <h3 data-section="subscriptions">🔔 Подписки и уведомления</h3>
                <p data-section="subscriptions_desc">Управляйте подписками на каналы и получайте дополнительные бонусы.</p>
                <div id="subscriptions-list" class="referrals-list">
                    <div style="background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 10px; padding: 20px; text-align: center; color: var(--cyber-text-secondary);">
                        📺 Подпишитесь на каналы<br>
                        <small style="opacity: 0.7;">Получайте бонусы за активные подписки</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- Нижняя навигация с компактными кибер-иконками -->
        <nav class="app-nav">
            <button class="nav-button active" id="nav-home">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                    <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
                <span class="nav-text" data-section="tasks">Главная</span>
            </button>
            <button class="nav-button" id="nav-earn">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="8"/>
                    <path d="M12 6v12"/>
                    <path d="M15 9l-3-3-3 3"/>
                    <path d="M9 15l3 3 3-3"/>
                </svg>
                <span class="nav-text" data-section="earnings">Заработок</span>
            </button>
            <button class="nav-button" id="nav-friends">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M23 21v-2a4 4 0 00-3-3.87"/>
                    <path d="M16 3.13a4 4 0 010 7.75"/>
                </svg>
                <span class="nav-text" data-section="referrals">Друзья</span>
            </button>
        </nav>

    </div> <!-- /app-container -->

    <!-- Подключение скриптов в конце -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
    <script src="js/localization.js"></script>
    <script src="main.js"></script>

    <script>
        console.log('💰 Cyberpunk coins background & glitch effects activated! 💜');

        // Добавляем кнопку для тестирования локализации (только в режиме разработки)
        if (window.location.hostname === 'argun-defolt.loc') {
            window.addEventListener('load', () => {
                const testButton = document.createElement('button');
                testButton.textContent = '🔧 Тест локализации';
                testButton.style.position = 'fixed';
                testButton.style.top = '10px';
                testButton.style.right = '10px';
                testButton.style.zIndex = '9999';
                testButton.style.background = '#ff00ff';
                testButton.style.color = 'white';
                testButton.style.border = 'none';
                testButton.style.padding = '5px 10px';
                testButton.style.borderRadius = '5px';
                testButton.style.fontSize = '12px';
                testButton.onclick = () => {
                    console.log('=== ПРИНУДИТЕЛЬНОЕ ПРИМЕНЕНИЕ ЛОКАЛИЗАЦИИ ===');
                    if (window.appLocalization) {
                        window.appLocalization.applyTranslations();
                        console.log('Локализация применена!');
                    } else {
                        console.log('Локализация не найдена!');
                    }
                };
                document.body.appendChild(testButton);
            });
        }
    </script>

</body>
</html>
