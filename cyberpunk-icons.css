/* ======================================== */
/* CYBERPUNK NAVIGATION ICONS */
/* ======================================== */

/* --- CYBERPUNK ICON STYLES --- */
.cyber-icon {
  width: 28px;
  height: 28px;
  margin-bottom: 6px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 3px rgba(0, 255, 255, 0.3));
}

.nav-button .cyber-icon {
  fill: var(--cyber-text-muted);
  stroke: var(--cyber-text-muted);
  stroke-width: 0.5;
}

.nav-button:hover .cyber-icon {
  fill: var(--cyber-text-secondary);
  stroke: var(--cyber-text-secondary);
  filter: drop-shadow(0 0 5px rgba(0, 255, 255, 0.4));
  transform: scale(1.1);
}

.nav-button.active .cyber-icon {
  fill: var(--cyber-accent-neon);
  stroke: var(--cyber-accent-neon);
  filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.6));
  transform: scale(1.15);
}

/* --- ICON ANIMATIONS --- */
@keyframes iconPulse {
  0%, 100% { 
    transform: scale(1);
    filter: drop-shadow(0 0 5px rgba(0, 255, 255, 0.4));
  }
  50% { 
    transform: scale(1.05);
    filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.8));
  }
}

.nav-button.active .cyber-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

/* --- CYBERPUNK GLOW EFFECTS --- */
.cyber-icon-glow {
  filter: 
    drop-shadow(0 0 3px var(--cyber-accent-neon))
    drop-shadow(0 0 6px var(--cyber-accent-neon))
    drop-shadow(0 0 9px var(--cyber-accent-neon));
}

/* --- ICON HOVER EFFECTS --- */
.nav-button:active .cyber-icon {
  transform: scale(0.95);
  filter: drop-shadow(0 0 12px rgba(0, 255, 255, 0.9));
}

/* --- RESPONSIVE ICON SIZES --- */
@media (max-width: 480px) {
  .cyber-icon {
    width: 24px;
    height: 24px;
  }
}

/* --- ICON SPECIFIC STYLES --- */
.cyber-icon.home-icon {
  stroke-width: 1.5;
}

.cyber-icon.earn-icon {
  stroke-width: 1;
}

.cyber-icon.friends-icon {
  stroke-width: 1.2;
}

/* --- CYBERPUNK ICON VARIANTS --- */
.cyber-icon-variant-1 {
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.cyber-icon-variant-2 {
  fill: currentColor;
  stroke: none;
}

.cyber-icon-variant-3 {
  fill: none;
  stroke: currentColor;
  stroke-width: 1.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 2,2;
}

/* --- GLITCH EFFECT FOR ICONS --- */
@keyframes iconGlitch {
  0%, 100% { 
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  20% { 
    transform: translate(-1px, 1px);
    filter: hue-rotate(90deg);
  }
  40% { 
    transform: translate(1px, -1px);
    filter: hue-rotate(180deg);
  }
  60% { 
    transform: translate(-1px, -1px);
    filter: hue-rotate(270deg);
  }
  80% { 
    transform: translate(1px, 1px);
    filter: hue-rotate(360deg);
  }
}

.nav-button.glitch-effect .cyber-icon {
  animation: iconGlitch 0.3s ease-in-out;
}

/* --- NEON BORDER EFFECTS --- */
.cyber-icon-border {
  border: 1px solid var(--cyber-accent-neon);
  border-radius: 8px;
  padding: 4px;
  background: rgba(0, 255, 255, 0.05);
  box-shadow: 
    0 0 5px rgba(0, 255, 255, 0.3),
    inset 0 0 5px rgba(0, 255, 255, 0.1);
}

/* --- CYBERPUNK ICON BACKGROUNDS --- */
.cyber-icon-bg-circuit {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 0, 128, 0.1) 0%, transparent 50%);
  border-radius: 50%;
  padding: 6px;
}

.cyber-icon-bg-hex {
  background: 
    linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(138, 43, 226, 0.1));
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  padding: 8px;
}

/* --- ICON LOADING STATES --- */
@keyframes iconLoading {
  0% { 
    opacity: 0.3;
    transform: rotate(0deg);
  }
  50% { 
    opacity: 0.7;
    transform: rotate(180deg);
  }
  100% { 
    opacity: 1;
    transform: rotate(360deg);
  }
}

.cyber-icon.loading {
  animation: iconLoading 1s linear infinite;
}

/* --- ICON SUCCESS/ERROR STATES --- */
.cyber-icon.success {
  fill: var(--cyber-success);
  stroke: var(--cyber-success);
  filter: drop-shadow(0 0 8px rgba(57, 255, 20, 0.6));
}

.cyber-icon.error {
  fill: var(--cyber-error);
  stroke: var(--cyber-error);
  filter: drop-shadow(0 0 8px rgba(255, 7, 58, 0.6));
}

/* --- ICON NOTIFICATION BADGES --- */
.cyber-icon-container {
  position: relative;
  display: inline-block;
}

.cyber-icon-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: linear-gradient(135deg, var(--cyber-accent-pink), var(--cyber-accent-purple));
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border: 1px solid var(--cyber-accent-neon);
  box-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  animation: iconPulse 1.5s ease-in-out infinite;
}

/* --- CYBERPUNK ICON THEMES --- */
.cyber-theme-neon .cyber-icon {
  filter: 
    drop-shadow(0 0 3px var(--cyber-accent-neon))
    drop-shadow(0 0 6px var(--cyber-accent-neon));
}

.cyber-theme-pink .cyber-icon {
  filter: 
    drop-shadow(0 0 3px var(--cyber-accent-pink))
    drop-shadow(0 0 6px var(--cyber-accent-pink));
}

.cyber-theme-purple .cyber-icon {
  filter: 
    drop-shadow(0 0 3px var(--cyber-accent-purple))
    drop-shadow(0 0 6px var(--cyber-accent-purple));
}

/* --- ICON INTERACTION FEEDBACK --- */
.cyber-icon-interactive {
  cursor: pointer;
  transition: all 0.2s ease;
}

.cyber-icon-interactive:hover {
  transform: scale(1.1) rotate(5deg);
}

.cyber-icon-interactive:active {
  transform: scale(0.95) rotate(-5deg);
}

/* --- CYBERPUNK ICON GRADIENTS --- */
.cyber-icon-gradient {
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* --- ICON MATRIX EFFECT --- */
@keyframes matrixRain {
  0% { 
    opacity: 0;
    transform: translateY(-10px);
  }
  50% { 
    opacity: 1;
    transform: translateY(0);
  }
  100% { 
    opacity: 0;
    transform: translateY(10px);
  }
}

.cyber-icon-matrix::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 255, 0.1) 2px,
      rgba(0, 255, 255, 0.1) 4px
    );
  animation: matrixRain 2s ease-in-out infinite;
  pointer-events: none;
}
