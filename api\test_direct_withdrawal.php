<?php
/**
 * Прямой тест вывода средств без валидации initData
 * Для тестирования реальных выплат NOWPayments
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🚀 ПРЯМОЙ ТЕСТ ВЫВОДА СРЕДСТВ ЧЕРЕЗ NOWPayments API\n\n";

// Параметры для реального теста - ВАШИ ДАННЫЕ
$testAmount = 1000; // 1000 монет как вы просили
$testAddress = '******************************************'; // ВАШ ETH адрес
$testCurrency = 'eth'; // Ethereum как вы просили

echo "📋 Параметры тестового вывода:\n";
echo "- Сумма: {$testAmount} монет\n";
echo "- Адрес: {$testAddress}\n";
echo "- Валюта: {$testCurrency}\n\n";

// Конвертируем монеты в USD
$usdAmount = $testAmount * CONVERSION_RATE;
echo "💵 Сумма в USD: \${$usdAmount}\n\n";

// Создаем экземпляр API клиента
$api = new NOWPaymentsAPI(
    NOWPAYMENTS_API_KEY, 
    NOWPAYMENTS_PUBLIC_KEY, 
    NOWPAYMENTS_IPN_SECRET, 
    NOWPAYMENTS_API_URL
);

echo "🔑 Проверяем подключение к NOWPayments API...\n";

// Проверяем баланс аккаунта
echo "💰 Получаем баланс аккаунта...\n";
$balance = $api->getAccountBalance();

if ($balance) {
    echo "✅ Баланс получен успешно:\n";
    foreach ($balance as $currency => $data) {
        $amount = $data['amount'] ?? 0;
        $pending = $data['pendingAmount'] ?? 0;
        $available = $amount - $pending;
        echo "- {$currency}: {$amount} (доступно: {$available})\n";
    }
    echo "\n";
} else {
    echo "❌ Не удалось получить баланс аккаунта\n";
    echo "Проверьте API ключи в config.php\n";
    exit;
}

// Получаем оценку суммы в выбранной криптовалюте
echo "🔄 Получаем оценку конвертации {$usdAmount} USD в {$testCurrency}...\n";
$estimate = $api->getEstimateAmount($usdAmount, 'usd', $testCurrency);

if (!$estimate || !isset($estimate['estimated_amount'])) {
    echo "❌ Не удалось получить оценку суммы для конвертации\n";
    echo "Ответ API: " . json_encode($estimate) . "\n";
    exit;
}

$cryptoAmount = $estimate['estimated_amount'];
echo "✅ Оценка получена: {$cryptoAmount} {$testCurrency}\n\n";

// Проверяем минимальную сумму для вывода
echo "📏 Проверяем минимальную сумму для {$testCurrency}...\n";
$minAmount = $api->getMinWithdrawalAmount($testCurrency);
if ($minAmount) {
    echo "ℹ️ Минимальная сумма: {$minAmount} {$testCurrency}\n";
    if ($cryptoAmount < $minAmount) {
        echo "⚠️ Сумма {$cryptoAmount} меньше минимальной {$minAmount}\n";
        echo "Но продолжаем тест для проверки логики API...\n\n";
    } else {
        echo "✅ Сумма соответствует минимальным требованиям\n\n";
    }
} else {
    echo "⚠️ Не удалось получить минимальную сумму, продолжаем...\n\n";
}

// Проверяем совместимость адреса с валютой
echo "🔍 Проверяем совместимость адреса с валютой...\n";
$isCompatible = $api->isAddressCompatible($testAddress, $testCurrency);
if ($isCompatible) {
    echo "✅ Адрес совместим с {$testCurrency}\n\n";
} else {
    echo "❌ Адрес НЕ совместим с {$testCurrency}\n";
    echo "Попробуем автоконвертацию...\n\n";
}

echo "🚀 СОЗДАЕМ РЕАЛЬНУЮ ВЫПЛАТУ...\n";
echo "⚠️ ВНИМАНИЕ: Это реальная транзакция!\n\n";

// Создаем выплату с автоконвертацией
echo "🔄 Пробуем автоконвертацию из доступного баланса...\n";
$result = $api->createPayoutWithAutoConversion($testAddress, $testCurrency, $cryptoAmount);

echo "📊 Результат создания выплаты:\n";
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

if (isset($result['error'])) {
    echo "❌ ОШИБКА: {$result['message']}\n";
    echo "Код ошибки: {$result['code']}\n";
    
    if (isset($result['details'])) {
        echo "Детали:\n";
        echo json_encode($result['details'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
} else {
    echo "🎉 ВЫПЛАТА СОЗДАНА УСПЕШНО!\n\n";
    
    if (isset($result['id'])) {
        echo "🆔 ID выплаты: {$result['id']}\n";
    }
    
    if (isset($result['withdrawals']) && is_array($result['withdrawals'])) {
        echo "📋 Детали выплаты:\n";
        foreach ($result['withdrawals'] as $withdrawal) {
            echo "- Адрес: {$withdrawal['address']}\n";
            echo "- Валюта: {$withdrawal['currency']}\n";
            echo "- Сумма: {$withdrawal['amount']}\n";
            echo "- Статус: {$withdrawal['status']}\n";
            if (isset($withdrawal['id'])) {
                echo "- ID: {$withdrawal['id']}\n";
            }
        }
    }
    
    if (isset($result['auto_conversion'])) {
        echo "\n🔄 Информация об автоконвертации:\n";
        $conv = $result['auto_conversion'];
        echo "- Запрошено: {$conv['original_request']['amount']} {$conv['original_request']['currency']}\n";
        echo "- Выплачено: {$conv['actual_payout']['amount']} {$conv['actual_payout']['currency']}\n";
        echo "- Адрес: {$conv['actual_payout']['address']}\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 ИТОГИ ТЕСТИРОВАНИЯ\n";
echo str_repeat("=", 60) . "\n\n";

if (!isset($result['error'])) {
    echo "🎉 ТЕСТ УСПЕШЕН!\n\n";
    echo "✅ Что протестировано:\n";
    echo "- Подключение к NOWPayments API\n";
    echo "- Получение баланса аккаунта\n";
    echo "- Конвертация валют\n";
    echo "- Создание реальной выплаты\n";
    echo "- Обработка автоконвертации\n\n";
    
    echo "🚀 Система работает корректно!\n";
    echo "Выплата отправлена на ваш Ethereum адрес.\n";
    echo "Проверьте транзакцию в блокчейне через несколько минут.\n";
} else {
    echo "⚠️ Обнаружены проблемы:\n";
    echo "- {$result['message']}\n\n";
    
    echo "🔧 Возможные решения:\n";
    if ($result['code'] === 'INSUFFICIENT_BALANCE') {
        echo "- Пополните баланс в панели NOWPayments\n";
    } elseif ($result['code'] === 'INCOMPATIBLE_ADDRESS') {
        echo "- Используйте адрес, совместимый с выбранной валютой\n";
    } else {
        echo "- Проверьте API ключи в config.php\n";
        echo "- Убедитесь, что аккаунт NOWPayments активен\n";
    }
}

echo "\n📝 Следующие шаги:\n";
echo "1. Проверьте созданную выплату в панели NOWPayments\n";
echo "2. Отследите транзакцию в блокчейне\n";
echo "3. Протестируйте через веб-интерфейс приложения\n";
?>
