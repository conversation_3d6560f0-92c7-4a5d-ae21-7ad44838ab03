<?php
/**
 * Тестовый файл для проверки обработки комиссий NOWPayments
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "<h1>Тест обработки комиссий NOWPayments</h1>\n";

// Тестируем получение минимальных сумм для разных валют
$currencies = ['BTC', 'ETH', 'USDT', 'LTC', 'TRX'];

echo "<h2>1. Минимальные суммы для выплат:</h2>\n";
foreach ($currencies as $currency) {
    echo "<h3>{$currency}:</h3>\n";
    $minAmount = $api->getMinWithdrawalAmount($currency);
    if ($minAmount !== null) {
        echo "Минимальная сумма: {$minAmount} {$currency}<br>\n";
    } else {
        echo "Не удалось получить минимальную сумму<br>\n";
    }
}

echo "<h2>2. Оценка комиссий:</h2>\n";
$testAmounts = [
    'BTC' => 0.001,
    'ETH' => 0.01,
    'USDT' => 10,
    'LTC' => 0.1,
    'TRX' => 100
];

foreach ($testAmounts as $currency => $amount) {
    echo "<h3>{$amount} {$currency}:</h3>\n";
    $feeEstimate = $api->getWithdrawalFeeEstimate($currency, $amount);
    if ($feeEstimate) {
        echo "Комиссия: " . json_encode($feeEstimate, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "<br>\n";
    } else {
        echo "Не удалось получить оценку комиссии<br>\n";
    }
}

echo "<h2>3. Тест создания выплаты с обработкой комиссий:</h2>\n";

// Тестовые данные
$testAddress = '**********************************'; // Genesis block address
$testCurrency = 'BTC';
$testAmount = 0.0001; // Очень маленькая сумма для тестирования минимума

echo "<h3>Тест с маленькой суммой ({$testAmount} {$testCurrency}):</h3>\n";
$result = $api->createPayoutWithFeeHandling($testAddress, $testCurrency, $testAmount);

if ($result) {
    echo "Результат: " . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "<br>\n";
    
    if (isset($result['error'])) {
        echo "<strong>Ошибка:</strong> " . $result['message'] . "<br>\n";
        if (isset($result['details'])) {
            echo "<strong>Детали:</strong><br>\n";
            foreach ($result['details'] as $key => $value) {
                echo "- {$key}: {$value}<br>\n";
            }
        }
    } else {
        echo "<strong>Выплата создана успешно!</strong><br>\n";
        if (isset($result['fee_handling'])) {
            echo "<strong>Информация о комиссии:</strong><br>\n";
            echo "- " . $result['fee_handling']['note'] . "<br>\n";
            if (isset($result['fee_handling']['fee_estimate'])) {
                echo "- Комиссия: " . json_encode($result['fee_handling']['fee_estimate']) . "<br>\n";
            }
        }
    }
} else {
    echo "Не удалось создать выплату<br>\n";
}

echo "<h2>4. Проверка баланса аккаунта:</h2>\n";
$balance = $api->getAccountBalance();
if ($balance) {
    echo "Баланс аккаунта: " . json_encode($balance, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "<br>\n";
} else {
    echo "Не удалось получить баланс аккаунта<br>\n";
}

echo "<h2>5. Список доступных валют:</h2>\n";
$currencies = $api->getAvailableCurrencies();
if ($currencies) {
    echo "Доступные валюты: " . json_encode($currencies, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "<br>\n";
} else {
    echo "Не удалось получить список валют<br>\n";
}

echo "<hr>\n";
echo "<p><strong>Тест завершен!</strong></p>\n";
echo "<p>Для решения проблемы с минимальными суммами:</p>\n";
echo "<ol>\n";
echo "<li>Войдите в панель NOWPayments: <a href='https://account.nowpayments.io/store-settings#details' target='_blank'>Store Settings</a></li>\n";
echo "<li>В разделе 'Payment details' найдите настройку 'Withdrawal fee paid by receiver'</li>\n";
echo "<li>Включите эту опцию, чтобы комиссия списывалась с получателя, а не с отправителя</li>\n";
echo "<li>Это позволит создавать выплаты с меньшими суммами</li>\n";
echo "</ol>\n";
?>
