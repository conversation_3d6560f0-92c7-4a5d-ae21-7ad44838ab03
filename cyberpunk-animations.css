/* ======================================== */
/* CYBERPUNK ADVANCED ANIMATIONS */
/* ======================================== */

/* Matrix-style data rain effect */
@keyframes dataRain {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

/* Holographic scan lines */
@keyframes scanLines {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100vh);
  }
}

/* Circuit board pulse */
@keyframes circuitPulse {
  0%, 100% {
    box-shadow: 
      0 0 5px var(--cyber-accent-neon),
      inset 0 0 5px var(--cyber-accent-neon);
  }
  50% {
    box-shadow: 
      0 0 20px var(--cyber-accent-neon),
      0 0 30px var(--cyber-accent-neon),
      inset 0 0 10px var(--cyber-accent-neon);
  }
}

/* Glitch distortion */
@keyframes glitchDistort {
  0% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-2px, 2px);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(2px, 2px);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
  50% {
    transform: translate(-2px, 2px);
    filter: hue-rotate(90deg);
  }
  60% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(180deg);
  }
  70% {
    transform: translate(2px, 2px);
    filter: hue-rotate(270deg);
  }
  80% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
  90% {
    transform: translate(-2px, 2px);
    filter: hue-rotate(90deg);
  }
  100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
}

/* Neon border animation */
@keyframes neonBorder {
  0% {
    border-color: var(--cyber-accent-neon);
    box-shadow: 0 0 5px var(--cyber-accent-neon);
  }
  25% {
    border-color: var(--cyber-accent-pink);
    box-shadow: 0 0 10px var(--cyber-accent-pink);
  }
  50% {
    border-color: var(--cyber-accent-purple);
    box-shadow: 0 0 15px var(--cyber-accent-purple);
  }
  75% {
    border-color: var(--cyber-accent-green);
    box-shadow: 0 0 10px var(--cyber-accent-green);
  }
  100% {
    border-color: var(--cyber-accent-neon);
    box-shadow: 0 0 5px var(--cyber-accent-neon);
  }
}

/* Hologram flicker */
@keyframes hologramFlicker {
  0%, 100% {
    opacity: 1;
    filter: brightness(1);
  }
  2% {
    opacity: 0.8;
    filter: brightness(1.2);
  }
  4% {
    opacity: 1;
    filter: brightness(0.8);
  }
  8% {
    opacity: 0.9;
    filter: brightness(1.1);
  }
  10% {
    opacity: 1;
    filter: brightness(1);
  }
}

/* Energy wave */
@keyframes energyWave {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Cyber loading spinner */
@keyframes cyberSpin {
  0% {
    transform: rotate(0deg);
    border-color: var(--cyber-accent-neon) transparent transparent transparent;
  }
  25% {
    border-color: var(--cyber-accent-pink) var(--cyber-accent-neon) transparent transparent;
  }
  50% {
    border-color: var(--cyber-accent-purple) var(--cyber-accent-pink) var(--cyber-accent-neon) transparent;
  }
  75% {
    border-color: var(--cyber-accent-green) var(--cyber-accent-purple) var(--cyber-accent-pink) var(--cyber-accent-neon);
  }
  100% {
    transform: rotate(360deg);
    border-color: var(--cyber-accent-neon) transparent transparent transparent;
  }
}

/* Text typing effect */
@keyframes typeWriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

/* Utility classes for animations */
.cyber-glitch {
  animation: glitchDistort 0.3s ease-in-out infinite;
}

.cyber-pulse {
  animation: circuitPulse 2s ease-in-out infinite;
}

.cyber-flicker {
  animation: hologramFlicker 3s ease-in-out infinite;
}

.cyber-border {
  animation: neonBorder 4s ease-in-out infinite;
}

.cyber-loading {
  width: 40px;
  height: 40px;
  border: 4px solid transparent;
  border-radius: 50%;
  animation: cyberSpin 1s linear infinite;
}

/* Advanced button effects */
.action-button.cyber-enhanced {
  position: relative;
  overflow: hidden;
}

.action-button.cyber-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.action-button.cyber-enhanced:hover::before {
  left: 100%;
}

.action-button.cyber-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-button.cyber-enhanced:hover::after {
  opacity: 1;
}

/* Scan line overlay */
.cyber-scanlines {
  position: relative;
  overflow: hidden;
}

.cyber-scanlines::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--cyber-accent-neon),
    transparent
  );
  animation: scanLines 3s linear infinite;
  opacity: 0.7;
}

/* Matrix rain effect */
.cyber-matrix {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.cyber-matrix::before {
  content: '01001001 01101110 01110100 01100101 01110010 01100001 01100011 01110100 01101001 01110110 01100101';
  position: absolute;
  top: -100px;
  left: 10%;
  color: var(--cyber-accent-green);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  opacity: 0.3;
  animation: dataRain 8s linear infinite;
  white-space: nowrap;
}

.cyber-matrix::after {
  content: '11000001 10101010 11110000 10101010 11000001 10101010 11110000 10101010 11000001 10101010';
  position: absolute;
  top: -100px;
  right: 20%;
  color: var(--cyber-accent-neon);
  font-family: 'Courier New', monospace;
  font-size: 10px;
  opacity: 0.2;
  animation: dataRain 12s linear infinite 2s;
  white-space: nowrap;
}

/* Holographic effect */
.cyber-hologram {
  position: relative;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 255, 255, 0.1) 50%,
    transparent 70%
  );
  animation: hologramFlicker 4s ease-in-out infinite;
}

.cyber-hologram::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 255, 0.03) 2px,
    rgba(0, 255, 255, 0.03) 4px
  );
  pointer-events: none;
}

/* Energy field effect */
.cyber-energy {
  position: relative;
}

.cyber-energy::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border: 2px solid var(--cyber-accent-neon);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: energyWave 2s ease-out infinite;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .cyber-matrix::before,
  .cyber-matrix::after {
    font-size: 8px;
  }
  
  .cyber-loading {
    width: 30px;
    height: 30px;
    border-width: 3px;
  }
}
