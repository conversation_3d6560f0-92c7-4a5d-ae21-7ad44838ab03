# 🤖 Настройка бота через @BotFather

## 📋 Пошаговая инструкция

### 1. Откройте @BotFather в Telegram
Перейдите к боту: https://t.me/BotFather

### 2. Измените название бота
```
/setname
@uniqpaid_bot
UniQPaid - Earn Crypto
```

### 3. Измените описание бота
```
/setdescription
@uniqpaid_bot
💰 Зарабатывайте криптовалюту за просмотр рекламы!

🚀 Моментальные выплаты на ваш кошелек
🎁 Реферальная программа - до 10% от друзей
💎 1 монета = $0.001 USD

Начните зарабатывать прямо сейчас!
```

### 4. Установите короткое описание
```
/setabouttext
@uniqpaid_bot
Зарабатывайте криптовалюту за просмотр рекламы! Моментальные выплаты и реферальная программа.
```

### 5. Загрузите аватар бота
```
/setuserpic
@uniqpaid_bot
```
Затем отправьте файл: `images/bot_avatar.svg` (конвертированный в PNG 512x512)

### 6. Настройте команды бота
```
/setcommands
@uniqpaid_bot
start - 🚀 Начать работу с ботом
balance - 💰 Показать баланс
help - ❓ Помощь и инструкции
```

### 7. Настройте меню бота
```
/setmenubutton
@uniqpaid_bot
text: 🚀 Открыть приложение
url: https://app.uniqpaid.com/test2/
```

### 8. Включите inline режим (опционально)
```
/setinline
@uniqpaid_bot
Поделиться UniQPaid
```

### 9. Настройте домен для Web App
```
/setdomain
@uniqpaid_bot
app.uniqpaid.com
```

## 🎨 Рекомендации по дизайну

### Цветовая схема:
- **Основной**: #4f46e5 (индиго)
- **Акцент**: #fbbf24 (золотой)
- **Градиент**: от #4f46e5 до #ec4899

### Ключевые слова для описания:
- Криптовалюта
- Заработок
- Реклама
- Моментальные выплаты
- Реферальная программа
- Web3
- DeFi

## 📱 Результат

После настройки ваш бот будет выглядеть профессионально:
- ✅ Красивая иконка с монетами
- ✅ Понятное название "UniQPaid - Earn Crypto"
- ✅ Подробное описание функций
- ✅ Настроенные команды
- ✅ Кнопка запуска приложения

## 🔗 Полезные ссылки

- Бот: https://t.me/uniqpaid_bot
- Приложение: https://app.uniqpaid.com/test2/
- BotFather: https://t.me/BotFather

## 📝 Примечания

1. **Аватар**: Конвертируйте SVG в PNG 512x512 пикселей
2. **Описание**: Максимум 512 символов
3. **Команды**: Максимум 100 команд
4. **Домен**: Должен совпадать с URL приложения
