<?php
/**
 * Тест комиссий для всех валют при минимальных суммах
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "💳 ТЕСТ КОМИССИЙ ДЛЯ ВСЕХ ВАЛЮТ\n";
echo str_repeat("=", 70) . "\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Валюты с их минимальными суммами (из предыдущего теста)
$currencies = [
    'usdttrc20' => ['name' => 'USDT (TRC20)', 'min_amount' => 8.58],
    'btc' => ['name' => 'Bitcoin (BTC)', 'min_amount' => 0.000005],
    'eth' => ['name' => 'Ethereum (ETH)', 'min_amount' => 0.001],
    'trx' => ['name' => 'TRON (TRX)', 'min_amount' => 1.0],
    'ltc' => ['name' => 'Litecoin (LTC)', 'min_amount' => 0.001],
    'bch' => ['name' => 'Bitcoin Cash (BCH)', 'min_amount' => 0.001],
    'xrp' => ['name' => 'Ripple (XRP)', 'min_amount' => 1.0],
    'ada' => ['name' => 'Cardano (ADA)', 'min_amount' => 1.0],
    'dot' => ['name' => 'Polkadot (DOT)', 'min_amount' => 0.1]
];

echo "🔍 Проверяем комиссии для " . count($currencies) . " валют:\n\n";

$results = [];
$successCount = 0;
$failCount = 0;

foreach ($currencies as $currency => $data) {
    $name = $data['name'];
    $minAmount = $data['min_amount'];
    
    echo "💰 {$name} ({$currency}):\n";
    echo "   📊 Минимальная сумма: {$minAmount}\n";
    
    // Получаем оценку комиссии для минимальной суммы
    $feeEstimate = $api->getWithdrawalFeeEstimate($currency, $minAmount);
    
    if ($feeEstimate && !isset($feeEstimate['error'])) {
        echo "   ✅ Комиссия получена:\n";
        
        // Показываем все доступные данные о комиссии
        if (isset($feeEstimate['fee'])) {
            echo "      💳 Размер комиссии: {$feeEstimate['fee']}\n";
        }
        
        if (isset($feeEstimate['currency'])) {
            echo "      💱 Валюта комиссии: {$feeEstimate['currency']}\n";
        }
        
        if (isset($feeEstimate['fee_type'])) {
            echo "      🏷️ Тип комиссии: {$feeEstimate['fee_type']}\n";
        }
        
        if (isset($feeEstimate['amount_to_receive'])) {
            echo "      📥 К получению: {$feeEstimate['amount_to_receive']}\n";
        }
        
        if (isset($feeEstimate['amount_to_send'])) {
            echo "      📤 К отправке: {$feeEstimate['amount_to_send']}\n";
        }
        
        // Рассчитываем процент комиссии
        if (isset($feeEstimate['fee']) && is_numeric($feeEstimate['fee'])) {
            $feePercent = ($feeEstimate['fee'] / $minAmount) * 100;
            echo "      📈 Процент комиссии: " . number_format($feePercent, 2) . "%\n";
        }
        
        $results[$currency] = [
            'name' => $name,
            'min_amount' => $minAmount,
            'fee_data' => $feeEstimate,
            'status' => 'success'
        ];
        $successCount++;
        
    } else {
        echo "   ❌ Не удалось получить комиссию\n";
        if ($feeEstimate && isset($feeEstimate['error'])) {
            echo "      🚫 Ошибка: {$feeEstimate['error']}\n";
        }
        
        $results[$currency] = [
            'name' => $name,
            'min_amount' => $minAmount,
            'fee_data' => null,
            'status' => 'failed'
        ];
        $failCount++;
    }
    
    echo "\n";
    
    // Небольшая пауза между запросами
    usleep(300000); // 0.3 секунды
}

echo str_repeat("=", 70) . "\n";
echo "📊 СВОДНАЯ ТАБЛИЦА КОМИССИЙ\n";
echo str_repeat("=", 70) . "\n\n";

echo sprintf("%-20s %-12s %-12s %-12s %-8s\n", "Валюта", "Мин. сумма", "Комиссия", "К получению", "Процент");
echo str_repeat("-", 70) . "\n";

$successResults = array_filter($results, function($r) { return $r['status'] === 'success'; });

foreach ($successResults as $currency => $data) {
    $name = $data['name'];
    $minAmount = $data['min_amount'];
    $feeData = $data['fee_data'];
    
    $fee = isset($feeData['fee']) ? $feeData['fee'] : 'N/A';
    $toReceive = isset($feeData['amount_to_receive']) ? $feeData['amount_to_receive'] : 'N/A';
    
    $percent = 'N/A';
    if (isset($feeData['fee']) && is_numeric($feeData['fee']) && $minAmount > 0) {
        $percent = number_format(($feeData['fee'] / $minAmount) * 100, 2) . '%';
    }
    
    echo sprintf("%-20s %-12s %-12s %-12s %-8s\n", 
        substr($name, 0, 19), 
        $minAmount, 
        $fee, 
        $toReceive, 
        $percent
    );
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "📈 АНАЛИЗ КОМИССИЙ\n";
echo str_repeat("=", 70) . "\n\n";

echo "📊 Статистика:\n";
echo "✅ Успешно получено: {$successCount}\n";
echo "❌ Ошибок: {$failCount}\n";
echo "📋 Всего проверено: " . count($currencies) . "\n\n";

// Анализируем комиссии
$feeAnalysis = [];
foreach ($successResults as $currency => $data) {
    if (isset($data['fee_data']['fee']) && is_numeric($data['fee_data']['fee'])) {
        $fee = (float)$data['fee_data']['fee'];
        $minAmount = $data['min_amount'];
        $percent = ($fee / $minAmount) * 100;
        
        $feeAnalysis[] = [
            'currency' => $currency,
            'name' => $data['name'],
            'fee' => $fee,
            'percent' => $percent,
            'min_amount' => $minAmount
        ];
    }
}

if (!empty($feeAnalysis)) {
    // Сортируем по проценту комиссии
    usort($feeAnalysis, function($a, $b) {
        return $a['percent'] <=> $b['percent'];
    });
    
    echo "🏆 РЕЙТИНГ ПО РАЗМЕРУ КОМИССИИ (от меньшей к большей):\n\n";
    
    foreach ($feeAnalysis as $index => $item) {
        $rank = $index + 1;
        $emoji = $rank <= 3 ? ['🥇', '🥈', '🥉'][$rank - 1] : "#{$rank}";
        
        echo "{$emoji} {$item['name']}: {$item['percent']}% ({$item['fee']} от {$item['min_amount']})\n";
    }
    
    echo "\n💡 РЕКОМЕНДАЦИИ:\n\n";
    
    $best = $feeAnalysis[0];
    $worst = end($feeAnalysis);
    
    echo "✅ Самая выгодная: {$best['name']} - {$best['percent']}%\n";
    echo "❌ Самая дорогая: {$worst['name']} - {$worst['percent']}%\n\n";
    
    echo "🎯 Для пользователей:\n";
    echo "- Для экономии на комиссиях выбирайте: {$best['name']}\n";
    echo "- Избегайте для маленьких сумм: {$worst['name']}\n";
}

echo "\n💾 СОХРАНЕНИЕ РЕЗУЛЬТАТОВ:\n";

// Сохраняем результаты в JSON файл
$jsonResults = [
    'timestamp' => date('Y-m-d H:i:s'),
    'total_currencies' => count($currencies),
    'success_count' => $successCount,
    'fail_count' => $failCount,
    'fee_analysis' => $feeAnalysis,
    'detailed_results' => $results
];

file_put_contents('fee_analysis_results.json', json_encode($jsonResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "📁 Результаты сохранены в файл: fee_analysis_results.json\n\n";

echo "🔧 СЛЕДУЮЩИЕ ШАГИ:\n";
echo "1. Обновить интерфейс с информацией о комиссиях\n";
echo "2. Добавить калькулятор комиссий для пользователей\n";
echo "3. Показывать итоговую сумму к получению\n";
echo "4. Рекомендовать оптимальные валюты\n\n";

echo str_repeat("=", 70) . "\n";
echo "🏁 Анализ комиссий завершён\n";
?>
