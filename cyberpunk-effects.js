// ======================================== //
// CYBERPUNK EFFECTS FOR TELEGRAM MINI APP //
// ======================================== //

class CyberpunkEffects {
    constructor() {
        this.isInitialized = false;
        this.glitchElements = [];
        this.neonElements = [];
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        console.log('🚀 Initializing Cyberpunk Effects...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEffects());
        } else {
            this.setupEffects();
        }
        
        this.isInitialized = true;
    }

    setupEffects() {
        this.createParticleSystem();
        this.setupButtonEffects();
        this.setupGlitchEffects();
        this.setupNeonEffects();
        this.setupHoverEffects();
        this.setupScrollEffects();
        
        console.log('✨ Cyberpunk effects activated!');
    }

    // Create floating particles background
    createParticleSystem() {
        const particleContainer = document.createElement('div');
        particleContainer.className = 'cyberpunk-particles';
        particleContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        `;

        // Create particles
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: ${Math.random() * 4 + 1}px;
                height: ${Math.random() * 4 + 1}px;
                background: ${this.getRandomNeonColor()};
                border-radius: 50%;
                opacity: ${Math.random() * 0.8 + 0.2};
                animation: float ${Math.random() * 10 + 5}s linear infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                box-shadow: 0 0 10px currentColor;
            `;
            particleContainer.appendChild(particle);
        }

        document.body.appendChild(particleContainer);

        // Add CSS animation for particles
        this.addParticleAnimations();
    }

    addParticleAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0% { transform: translateY(100vh) rotate(0deg); }
                100% { transform: translateY(-100px) rotate(360deg); }
            }
            
            @keyframes cyberpunkPulse {
                0%, 100% { 
                    box-shadow: 0 0 5px currentColor, 0 0 10px currentColor;
                    transform: scale(1);
                }
                50% { 
                    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
                    transform: scale(1.1);
                }
            }
            
            @keyframes dataStream {
                0% { opacity: 0; transform: translateY(-20px); }
                50% { opacity: 1; }
                100% { opacity: 0; transform: translateY(20px); }
            }
        `;
        document.head.appendChild(style);
    }

    // Enhanced button effects
    setupButtonEffects() {
        const buttons = document.querySelectorAll('.action-button, .nav-button');
        
        buttons.forEach(button => {
            // Add ripple effect on click
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
                this.addButtonPulse(button);
            });

            // Add hover sound effect (visual feedback)
            button.addEventListener('mouseenter', () => {
                this.addHoverGlow(button);
            });

            button.addEventListener('mouseleave', () => {
                this.removeHoverGlow(button);
            });
        });
    }

    createRippleEffect(event, element) {
        const ripple = document.createElement('div');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: radial-gradient(circle, rgba(0, 255, 255, 0.6) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            animation: ripple 0.6s ease-out;
            z-index: 1000;
        `;

        element.style.position = 'relative';
        element.appendChild(ripple);

        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                0% { transform: scale(0); opacity: 1; }
                100% { transform: scale(2); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    addButtonPulse(button) {
        button.style.animation = 'cyberpunkPulse 0.3s ease-out';
        setTimeout(() => {
            button.style.animation = '';
        }, 300);
    }

    addHoverGlow(element) {
        element.style.filter = 'brightness(1.2) drop-shadow(0 0 10px rgba(0, 255, 255, 0.5))';
    }

    removeHoverGlow(element) {
        element.style.filter = '';
    }

    // Glitch effects for error states
    setupGlitchEffects() {
        const errorElements = document.querySelectorAll('.error-message, .status-message.error');
        
        errorElements.forEach(element => {
            this.addGlitchEffect(element);
        });

        // Monitor for new error messages
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) {
                        const errorElements = node.querySelectorAll('.error-message, .status-message.error');
                        errorElements.forEach(element => this.addGlitchEffect(element));
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    addGlitchEffect(element) {
        if (element.dataset.glitchAdded) return;
        element.dataset.glitchAdded = 'true';

        const originalText = element.textContent;
        let glitchInterval;

        const startGlitch = () => {
            glitchInterval = setInterval(() => {
                if (Math.random() > 0.7) {
                    element.textContent = this.glitchText(originalText);
                    setTimeout(() => {
                        element.textContent = originalText;
                    }, 100);
                }
            }, 200);
        };

        const stopGlitch = () => {
            clearInterval(glitchInterval);
            element.textContent = originalText;
        };

        element.addEventListener('mouseenter', startGlitch);
        element.addEventListener('mouseleave', stopGlitch);
    }

    glitchText(text) {
        const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        return text.split('').map(char => {
            return Math.random() > 0.8 ? glitchChars[Math.floor(Math.random() * glitchChars.length)] : char;
        }).join('');
    }

    // Neon glow effects
    setupNeonEffects() {
        const neonElements = document.querySelectorAll('h2, .balance-amount, .stat-value');
        
        neonElements.forEach(element => {
            this.addNeonGlow(element);
        });
    }

    addNeonGlow(element) {
        element.style.textShadow = '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor';
        
        // Pulsing effect
        setInterval(() => {
            if (Math.random() > 0.9) {
                element.style.textShadow = '0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor';
                setTimeout(() => {
                    element.style.textShadow = '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor';
                }, 200);
            }
        }, 1000);
    }

    // Enhanced hover effects
    setupHoverEffects() {
        const hoverElements = document.querySelectorAll('.friends-block, .earn-block, .withdrawal-item');
        
        hoverElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.style.transform = 'translateY(-5px) scale(1.02)';
                element.style.boxShadow = '0 10px 30px rgba(0, 255, 255, 0.2), 0 0 50px rgba(0, 255, 255, 0.1)';
            });

            element.addEventListener('mouseleave', () => {
                element.style.transform = '';
                element.style.boxShadow = '';
            });
        });
    }

    // Scroll-triggered effects
    setupScrollEffects() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInCyber 0.6s ease-out';
                    entry.target.style.opacity = '1';
                }
            });
        }, observerOptions);

        // Observe all content blocks
        const blocks = document.querySelectorAll('.friends-block, .earn-block, .action-button');
        blocks.forEach(block => {
            block.style.opacity = '0';
            observer.observe(block);
        });
    }

    // Utility functions
    getRandomNeonColor() {
        const colors = ['#00ffff', '#ff0080', '#8a2be2', '#39ff14', '#ff6600'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // Public methods for manual triggering
    triggerGlitch(element) {
        element.style.animation = 'glitch 0.5s ease-in-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 500);
    }

    triggerNeonFlash(element) {
        element.style.animation = 'neonGlow 1s ease-in-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 1000);
    }
}

// Initialize cyberpunk effects when script loads
const cyberpunkFX = new CyberpunkEffects();

// Export for global access
window.CyberpunkEffects = cyberpunkFX;
