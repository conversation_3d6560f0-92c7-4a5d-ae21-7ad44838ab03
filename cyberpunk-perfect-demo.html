<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>🚀 CyberApp Perfect - Раскрытые блоки</title>
    <link rel="stylesheet" href="cyberpunk-styles.css">
    <style>
        /* Perfect demo styles */
        .demo-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
            color: var(--cyber-bg-primary);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            z-index: 9999;
            animation: pulse 2s ease-in-out infinite;
        }
        
        .perfect-content {
            background: rgba(0, 255, 255, 0.03);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .feature-list li {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            margin: 5px 0;
            color: var(--cyber-text-primary);
            font-size: 14px;
        }
        
        .feature-list li::before {
            content: "⚡ ";
            color: var(--cyber-accent-neon);
        }
        
        .crypto-card {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(138, 43, 226, 0.1));
            border: 2px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        
        .crypto-card h4 {
            color: var(--cyber-accent-neon);
            margin: 0 0 10px 0;
            font-family: 'Orbitron', monospace;
        }
        
        .crypto-card p {
            color: var(--cyber-text-secondary);
            margin: 5px 0;
            font-size: 14px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }
        
        .stat-card {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--cyber-border);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: var(--cyber-accent-neon);
            font-family: 'Orbitron', monospace;
            text-shadow: 0 0 5px var(--cyber-glow);
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--cyber-text-secondary);
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="demo-badge">PERFECT ✨</div>

    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="./images/user.svg" class="user-avatar-icon" alt="user">
                </div>
                <div class="user-name">CyberUser_Perfect</div>
            </div>
            <div class="balance-info">
                <span class="balance-amount">2,077</span>
                <span class="balance-currency">монет</span>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main active-section" id="main-content">
            <div class="status-message success">
                🎉 Все блоки теперь полностью раскрыты и красивы!
            </div>

            <h2>⚡ CYBER MISSIONS</h2>

            <button class="action-button purple-button">
                🔮 ОТКРЫТЬ ПОРТАЛ
            </button>

            <button class="action-button blue-button">
                📺 СМОТРЕТЬ ГОЛОГРАММУ
            </button>

            <button class="action-button orange-button">
                🔥 АКТИВИРОВАТЬ РЕКЛАМУ
            </button>

            <div class="friends-block">
                <h3>🌟 Особенности Perfect Edition</h3>
                <p>Все блоки теперь имеют полное содержимое и выглядят потрясающе!</p>
                <ul class="feature-list">
                    <li>Полностью раскрытые блоки с контентом</li>
                    <li>Красивые описания и иконки</li>
                    <li>Правильные размеры и отступы</li>
                    <li>Идеальная кибер-панк стилизация</li>
                    <li>Отзывчивый и интерактивный дизайн</li>
                </ul>
            </div>

            <div class="friends-block">
                <h3>📊 Статистика системы</h3>
                <p>Мониторинг активности и производительности кибер-сети.</p>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">2,077</div>
                        <div class="stat-label">Активных агентов</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">∞</div>
                        <div class="stat-label">Выполнено миссий</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Earn Section -->
        <section class="app-section page-hidden" id="earn-section">
            <h2>💰 CYBER BANK</h2>
            
            <div class="status-message">
                💎 Добро пожаловать в банковскую систему будущего!
            </div>

            <div class="earn-block">
                <h3>💎 Ваш кибер-счет</h3>
                <p>Текущий баланс вашего аккаунта и доступные для операций средства.</p>
                <div class="perfect-content">
                    <div style="text-align: center; padding: 20px;">
                        <div class="stat-number" style="font-size: 32px;">2,077</div>
                        <div style="color: var(--cyber-text-primary); font-size: 16px; margin-top: 5px;">монет</div>
                    </div>
                    <p style="text-align: center; color: var(--cyber-accent-neon); margin: 10px 0;">
                        Доступно для вывода: <strong>2,077 монет</strong>
                    </p>
                </div>
            </div>

            <div class="earn-block">
                <h3>🏦 Криптовалютный обмен</h3>
                <p>Конвертируйте ваши монеты в популярные криптовалюты с минимальными комиссиями.</p>
                
                <div class="crypto-card">
                    <h4>⭐ Ethereum (ETH)</h4>
                    <p>Курс: 1,000 монет = $1.00</p>
                    <p>Комиссия сети: $0.53</p>
                    <p style="color: var(--cyber-accent-neon);">Рекомендуется</p>
                </div>

                <div class="crypto-card">
                    <h4>₿ Bitcoin (BTC)</h4>
                    <p>Курс: 1,000 монет = $1.00</p>
                    <p>Комиссия сети: $2.15</p>
                    <p style="color: var(--cyber-warning);">Высокая комиссия</p>
                </div>
            </div>

            <div class="earn-block">
                <h3>📝 Заявка на вывод</h3>
                <p>Выберите криптовалюту и укажите адрес кошелька для получения средств.</p>
                <div class="perfect-content">
                    <div style="margin: 15px 0;">
                        <label style="color: var(--cyber-text-primary); font-weight: bold; display: block; margin-bottom: 8px;">Выбранная валюта:</label>
                        <div style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-border); border-radius: 8px; padding: 10px;">
                            ⭐ Ethereum (ETH)
                        </div>
                    </div>
                    <div style="margin: 15px 0;">
                        <label style="color: var(--cyber-text-primary); font-weight: bold; display: block; margin-bottom: 8px;">Адрес кошелька:</label>
                        <div style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-border); border-radius: 8px; padding: 10px; font-family: monospace; font-size: 12px;">
                            0x742d35Cc6634C0532925a3b8D4...
                        </div>
                    </div>
                    <button class="action-button purple-button" style="width: 100%; margin-top: 15px;">
                        💸 Запросить вывод
                    </button>
                </div>
            </div>

            <div class="earn-block">
                <h3>📊 История транзакций</h3>
                <p>Полная история всех ваших операций и их текущие статусы.</p>
                <div class="perfect-content">
                    <div style="text-align: center; padding: 30px; color: var(--cyber-text-secondary);">
                        <div style="font-size: 48px; margin-bottom: 15px;">📋</div>
                        <div style="font-size: 16px; margin-bottom: 8px;">История транзакций пуста</div>
                        <div style="font-size: 14px; opacity: 0.7;">Выполните первую миссию для получения монет</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Friends Section -->
        <section class="app-section page-hidden" id="friends-section">
            <h2>👥 CYBER NETWORK</h2>
            
            <div class="status-message">
                🌐 Подключение к глобальной сети агентов установлено!
            </div>

            <div class="friends-block">
                <h3>📱 Поделиться приложением</h3>
                <p>Расскажите друзьям об этом потрясающем кибер-панк приложении и зарабатывайте вместе!</p>
                <div class="perfect-content">
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 48px; margin-bottom: 15px;">🚀</div>
                        <button class="action-button purple-button">
                            Поделиться в Telegram
                        </button>
                    </div>
                </div>
            </div>

            <div class="friends-block">
                <h3>🔗 Реферальная программа</h3>
                <p>Получайте 10% от заработка каждого приглашенного друга навсегда!</p>
                <div class="perfect-content">
                    <div style="margin: 15px 0;">
                        <label style="color: var(--cyber-text-primary); font-weight: bold; display: block; margin-bottom: 8px;">Ваша реферальная ссылка:</label>
                        <div style="background: rgba(0, 255, 255, 0.1); border: 1px solid var(--cyber-border); border-radius: 8px; padding: 10px; font-family: monospace; font-size: 12px; display: flex; justify-content: space-between; align-items: center;">
                            <span>https://t.me/cyberapp_bot?start=ref_2077</span>
                            <button style="background: var(--cyber-accent-neon); border: none; border-radius: 5px; padding: 5px 10px; color: var(--cyber-bg-primary); font-weight: bold;">📋</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="friends-block">
                <h3>📈 Сетевая статистика</h3>
                <p>Отслеживайте свои успехи в расширении кибер-сети.</p>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Агентов в сети</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <div class="stat-label">Заработано монет</div>
                    </div>
                </div>
                <div class="perfect-content">
                    <div style="text-align: center; padding: 20px; color: var(--cyber-text-secondary);">
                        <div style="font-size: 48px; margin-bottom: 15px;">👥</div>
                        <div style="font-size: 16px; margin-bottom: 8px;">Станьте первым в рейтинге!</div>
                        <div style="font-size: 14px; opacity: 0.7;">Приглашайте друзей и выполняйте миссии</div>
                    </div>
                </div>
            </div>

            <div class="friends-block">
                <h3>🔔 Подписки и бонусы</h3>
                <p>Подписывайтесь на каналы партнеров и получайте дополнительные награды.</p>
                <div class="perfect-content">
                    <div style="text-align: center; padding: 20px; color: var(--cyber-text-secondary);">
                        <div style="font-size: 48px; margin-bottom: 15px;">📺</div>
                        <div style="font-size: 16px; margin-bottom: 8px;">Подпишитесь на каналы</div>
                        <div style="font-size: 14px; opacity: 0.7;">Получайте бонусы за активные подписки</div>
                        <button class="action-button blue-button" style="margin-top: 15px;">
                            🔔 Показать каналы
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation -->
        <nav class="app-nav">
            <button class="nav-button active" id="nav-home">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                    <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
                <span>ГЛАВНАЯ</span>
            </button>
            
            <button class="nav-button" id="nav-earn">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="8"/>
                    <path d="M12 6v12"/>
                    <path d="M15 9l-3-3-3 3"/>
                    <path d="M9 15l3 3 3-3"/>
                </svg>
                <span>БАНК</span>
            </button>
            
            <button class="nav-button" id="nav-friends">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M23 21v-2a4 4 0 00-3-3.87"/>
                    <path d="M16 3.13a4 4 0 010 7.75"/>
                </svg>
                <span>СЕТЬ</span>
            </button>
        </nav>
    </div>

    <script src="cyberpunk-navigation-fix.js"></script>
    <script>
        console.log('🎉 Perfect Demo loaded! All blocks are now fully expanded and beautiful! 💜');
    </script>
</body>
</html>
