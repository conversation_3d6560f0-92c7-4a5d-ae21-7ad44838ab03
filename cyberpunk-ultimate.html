<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>🚀 CyberApp 2077 - Ultimate Edition</title>
    <link rel="stylesheet" href="cyberpunk-styles.css">
    <style>
        /* Ultimate improvements */
        .app-header {
            backdrop-filter: blur(20px) saturate(180%);
            background: linear-gradient(135deg, 
                rgba(26, 26, 46, 0.98) 0%, 
                rgba(22, 33, 62, 0.98) 50%,
                rgba(16, 21, 62, 0.98) 100%);
            border-bottom: 2px solid transparent;
            border-image: linear-gradient(90deg, var(--cyber-accent-neon), var(--cyber-accent-pink), var(--cyber-accent-purple)) 1;
        }
        
        .user-avatar {
            position: relative;
            overflow: visible;
        }
        
        .user-avatar::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: linear-gradient(45deg, var(--cyber-accent-neon), var(--cyber-accent-pink), var(--cyber-accent-purple));
            z-index: -1;
            animation: neonGlow 3s ease-in-out infinite;
        }
        
        .balance-info {
            position: relative;
            overflow: hidden;
        }
        
        .balance-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }
        
        .balance-info:hover::before {
            left: 100%;
        }
        
        .action-button {
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
        }
        
        .action-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
            border-radius: 50%;
        }
        
        .action-button:active::after {
            width: 300px;
            height: 300px;
        }
        
        .nav-button {
            position: relative;
            overflow: hidden;
        }
        
        .nav-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(138, 43, 226, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 12px;
        }
        
        .nav-button.active::before {
            opacity: 1;
        }
        
        .cyber-icon {
            filter: drop-shadow(0 0 3px currentColor);
            transition: all 0.3s ease;
        }
        
        .nav-button.active .cyber-icon {
            filter: drop-shadow(0 0 8px var(--cyber-accent-neon));
            animation: iconPulse 2s ease-in-out infinite;
        }
        
        @keyframes iconPulse {
            0%, 100% { 
                transform: scale(1);
                filter: drop-shadow(0 0 8px var(--cyber-accent-neon));
            }
            50% { 
                transform: scale(1.1);
                filter: drop-shadow(0 0 12px var(--cyber-accent-neon));
            }
        }
        
        .status-message {
            position: relative;
            overflow: hidden;
        }
        
        .status-message.success::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(57, 255, 20, 0.3), transparent);
            animation: successSweep 2s ease-in-out infinite;
        }
        
        @keyframes successSweep {
            0% { left: -100%; }
            50% { left: 100%; }
            100% { left: -100%; }
        }
        
        .app-main {
            position: relative;
        }
        
        .app-main::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 0, 128, 0.03) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .ultimate-glow {
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.3),
                0 0 40px rgba(0, 255, 255, 0.2),
                0 0 60px rgba(0, 255, 255, 0.1);
        }
        
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--cyber-accent-neon);
            border-radius: 50%;
            animation: float 6s linear infinite;
            opacity: 0.6;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 0.6;
            }
            90% {
                opacity: 0.6;
            }
            100% {
                transform: translateY(-10px) translateX(100px);
                opacity: 0;
            }
        }
        
        .particle:nth-child(2) { left: 20%; animation-delay: -1s; background: var(--cyber-accent-pink); }
        .particle:nth-child(3) { left: 40%; animation-delay: -2s; background: var(--cyber-accent-purple); }
        .particle:nth-child(4) { left: 60%; animation-delay: -3s; background: var(--cyber-accent-green); }
        .particle:nth-child(5) { left: 80%; animation-delay: -4s; background: var(--cyber-accent-neon); }
    </style>
</head>
<body>
    <!-- Floating Particles -->
    <div class="floating-particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <div class="app-container">
        <!-- Ultimate Header -->
        <header class="app-header ultimate-glow">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="./images/user.svg" class="user-avatar-icon" alt="user">
                </div>
                <div class="user-name" id="user-name">CyberUser_2077</div>
            </div>
            <div class="balance-info clickable-balance" id="header-balance-info" title="Баланс">
                <span class="balance-amount" id="balance-amount">1,337</span>
                <span class="balance-currency">монет</span>
            </div>
        </header>

        <!-- Ultimate Main Content -->
        <main class="app-main active-section" id="main-content">
            <div class="status-message success" id="status-message">
                🚀 Добро пожаловать в будущее! Система активирована.
            </div>

            <h2>⚡ CYBER MISSIONS</h2>

            <button id="openLinkButton" class="action-button purple-button">
                🔮 ОТКРЫТЬ ПОРТАЛ
            </button>

            <button id="watchVideoButton" class="action-button blue-button">
                📺 СМОТРЕТЬ ГОЛОГРАММУ
            </button>

            <button id="openAdButton" class="action-button orange-button">
                🔥 АКТИВИРОВАТЬ РЕКЛАМУ
            </button>

            <div class="friends-block" style="margin-top: 30px;">
                <h3>🌐 Статистика системы</h3>
                <div class="referral-stats">
                    <div class="stat-item">
                        <div class="stat-label">Активных пользователей:</div>
                        <div class="stat-value">2,077</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Выполнено миссий:</div>
                        <div class="stat-value">∞</div>
                    </div>
                </div>
            </div>

            <div class="friends-block">
                <h3>🎯 Достижения</h3>
                <p style="color: var(--cyber-text-secondary); line-height: 1.6;">
                    🏆 <strong>Кибер-новичок</strong> - Первый вход в систему<br>
                    ⚡ <strong>Энергетик</strong> - 10 выполненных заданий<br>
                    🚀 <strong>Хакер</strong> - 100 монет заработано<br>
                    💎 <strong>Легенда</strong> - 1000+ монет в балансе
                </p>
            </div>

            <div class="friends-block">
                <h3>🔬 Системная информация</h3>
                <div style="font-family: 'Orbitron', monospace; font-size: 12px; color: var(--cyber-text-muted); line-height: 1.8;">
                    <div>🖥️ OS: CyberOS 2077.1</div>
                    <div>⚡ CPU: Neural Core X1</div>
                    <div>🧠 RAM: 16GB Quantum</div>
                    <div>🌐 NET: HyperLink 5G</div>
                    <div>🔋 PWR: 100% Fusion</div>
                    <div>🛡️ SEC: Military Grade</div>
                </div>
            </div>
        </main>

        <!-- Earn Section -->
        <section class="app-section page-hidden" id="earn-section">
            <h2>💰 CYBER BANK</h2>
            
            <div class="status-message">
                💎 Добро пожаловать в банковскую систему будущего!
            </div>

            <div class="earn-block">
                <h3>💳 Ваш кибер-счет</h3>
                <div class="current-balance-display">
                    <span class="balance-amount" id="earn-balance-amount" style="font-size: 24px;">1,337</span>
                    <span class="balance-currency" style="font-size: 16px;">монет</span>
                </div>
                <p class="hint">Доступно для вывода: <span id="available-withdrawal" style="color: var(--cyber-accent-neon);">1,337</span> монет.</p>
            </div>

            <div class="earn-block">
                <h3>🏦 Криптовалютный обмен</h3>
                <p style="color: var(--cyber-text-secondary);">
                    Конвертируйте ваши монеты в популярные криптовалюты с минимальными комиссиями.
                </p>
                
                <div class="currency-tabs-header" style="margin-top: 20px;">
                    <button class="currency-tab active" data-currency="eth">
                        <span class="tab-icon">⭐</span>
                        <span class="tab-name">Ethereum</span>
                        <span class="tab-symbol">ETH</span>
                    </button>
                    <button class="currency-tab" data-currency="btc">
                        <span class="tab-icon">₿</span>
                        <span class="tab-name">Bitcoin</span>
                        <span class="tab-symbol">BTC</span>
                    </button>
                </div>
            </div>

            <div class="earn-block">
                <h3>📊 История транзакций</h3>
                <div style="background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 10px; padding: 15px;">
                    <div style="color: var(--cyber-text-secondary); text-align: center; padding: 20px;">
                        🔍 История транзакций пуста<br>
                        <small>Выполните первую миссию для получения монет</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- Friends Section -->
        <section class="app-section page-hidden" id="friends-section">
            <h2>👥 CYBER NETWORK</h2>
            
            <div class="status-message">
                🌐 Подключение к глобальной сети агентов...
            </div>

            <div class="friends-block">
                <h3>🔗 Пригласительная ссылка</h3>
                <p>Расширьте свою сеть и получайте бонусы от активности друзей!</p>
                <div class="referral-link-area">
                    <input type="text" id="referral-link-input" value="https://t.me/cyberapp_bot?start=ref_2077" readonly>
                    <button id="copy-referral-button" class="copy-button" title="Копировать">
                        📋
                    </button>
                </div>
            </div>

            <div class="friends-block">
                <h3>📈 Сетевая статистика</h3>
                <div class="referral-stats">
                    <div class="stat-item">
                        <div class="stat-label">Агентов в сети:</div>
                        <div class="stat-value" id="referrals-count">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Заработано с сети:</div>
                        <div class="stat-value" id="referral-earnings">0</div>
                    </div>
                </div>
            </div>

            <div class="friends-block">
                <h3>🎖️ Рейтинг агентов</h3>
                <div style="background: rgba(0, 255, 255, 0.05); border: 1px solid var(--cyber-border); border-radius: 10px; padding: 15px;">
                    <div style="color: var(--cyber-text-secondary); text-align: center; padding: 20px;">
                        🏆 Станьте первым в рейтинге!<br>
                        <small>Приглашайте друзей и выполняйте миссии</small>
                    </div>
                </div>
            </div>
        </section>

        <!-- Ultimate Navigation -->
        <nav class="app-nav ultimate-glow">
            <button class="nav-button active" id="nav-home">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                    <polyline points="9,22 9,12 15,12 15,22"/>
                </svg>
                <span>ГЛАВНАЯ</span>
            </button>
            
            <button class="nav-button" id="nav-earn">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="8"/>
                    <path d="M12 6v12"/>
                    <path d="M15 9l-3-3-3 3"/>
                    <path d="M9 15l3 3 3-3"/>
                </svg>
                <span>БАНК</span>
            </button>
            
            <button class="nav-button" id="nav-friends">
                <svg class="cyber-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M23 21v-2a4 4 0 00-3-3.87"/>
                    <path d="M16 3.13a4 4 0 010 7.75"/>
                </svg>
                <span>СЕТЬ</span>
            </button>
        </nav>
    </div>

    <script src="cyberpunk-navigation-fix.js"></script>
    <script>
        // Ultimate enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced button effects
            document.querySelectorAll('.action-button').forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // Copy functionality
            document.getElementById('copy-referral-button')?.addEventListener('click', function() {
                const input = document.getElementById('referral-link-input');
                input.select();
                document.execCommand('copy');
                
                const originalText = this.textContent;
                this.textContent = '✅';
                this.style.background = 'linear-gradient(135deg, var(--cyber-success), var(--cyber-accent-green))';
                
                setTimeout(() => {
                    this.textContent = originalText;
                    this.style.background = '';
                }, 2000);
            });

            // Dynamic balance updates
            let balance = 1337;
            setInterval(() => {
                balance += Math.floor(Math.random() * 3);
                document.querySelectorAll('.balance-amount').forEach(el => {
                    if (el.id === 'balance-amount' || el.id === 'earn-balance-amount') {
                        el.textContent = balance.toLocaleString();
                    }
                });
            }, 5000);

            console.log('🚀 CyberApp 2077 Ultimate Edition loaded! Welcome to the future! 💜⚡');
        });
    </script>
</body>
</html>
