<?php
/**
 * Быстрый тест API без веб-интерфейса
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "=== БЫСТРЫЙ ТЕСТ NOWPayments API ===\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// 1. Проверяем баланс
echo "1. Проверка баланса аккаунта:\n";
$balance = $api->getAccountBalance();
if ($balance) {
    foreach ($balance as $currency => $amount) {
        // Обрабатываем случай, когда amount может быть массивом
        $displayAmount = is_array($amount) ? json_encode($amount) : $amount;
        if ((is_numeric($amount) && $amount > 0) || is_array($amount)) {
            echo "   {$currency}: {$displayAmount}\n";
        }
    }
} else {
    echo "   ❌ Не удалось получить баланс\n";
}
echo "\n";

// 2. Проверяем минимальные суммы
echo "2. Минимальные суммы для выплат:\n";
$currencies = ['BTC', 'ETH', 'USDT', 'LTC', 'TRX'];
foreach ($currencies as $currency) {
    $minAmount = $api->getMinWithdrawalAmount($currency);
    if ($minAmount !== null) {
        echo "   {$currency}: {$minAmount}\n";
    } else {
        echo "   {$currency}: не удалось получить\n";
    }
}
echo "\n";

// 3. Тестируем создание выплаты с очень маленькой суммой
echo "3. Тест создания выплаты с маленькой суммой:\n";
$testAddress = '**********************************'; // Genesis block address
$testCurrency = 'BTC';
$testAmount = 0.0001; // Маленькая, но реалистичная сумма

echo "   Параметры: {$testAmount} {$testCurrency} на адрес {$testAddress}\n";

// Сначала проверим минимальную сумму для BTC
$minBTC = $api->getMinWithdrawalAmount('BTC');
echo "   Минимальная сумма BTC: " . ($minBTC ?? 'не получена') . "\n";

// Проверим оценку комиссии
$feeEstimate = $api->getWithdrawalFeeEstimate('BTC', $testAmount);
echo "   Оценка комиссии: " . ($feeEstimate ? json_encode($feeEstimate) : 'не получена') . "\n";

$result = $api->createPayoutWithFeeHandling($testAddress, $testCurrency, $testAmount);

if ($result) {
    if (isset($result['error'])) {
        echo "   ❌ Ошибка: {$result['message']}\n";
        if (isset($result['details'])) {
            echo "   Детали:\n";
            foreach ($result['details'] as $key => $value) {
                echo "     - {$key}: {$value}\n";
            }
        }
    } else {
        echo "   ✅ Выплата создана успешно!\n";
        if (isset($result['id'])) {
            echo "   ID: {$result['id']}\n";
        }
        if (isset($result['fee_handling'])) {
            echo "   Комиссия: {$result['fee_handling']['note']}\n";
        }
    }
} else {
    echo "   ❌ Критическая ошибка создания выплаты\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
