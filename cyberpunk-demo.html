<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyberpunk Demo - Applanza App</title>
    <link rel="stylesheet" href="cyberpunk-styles.css">
    <style>
        /* Demo-specific styles */
        .demo-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            text-align: center;
            font-family: 'Orbitron', monospace;
            color: var(--cyber-accent-neon);
            text-shadow: 0 0 10px var(--cyber-glow);
            margin-bottom: 30px;
            font-size: 28px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .feature-showcase {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .feature-item h3 {
            color: var(--cyber-accent-neon);
            font-family: 'Orbitron', monospace;
            margin-bottom: 10px;
        }
        
        .feature-item p {
            color: var(--cyber-text-secondary);
            font-family: 'Rajdhani', sans-serif;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🚀 Cyberpunk Design Demo</h1>
        
        <!-- Header Demo -->
        <div class="demo-section">
            <div class="app-header" style="position: relative; margin-bottom: 20px;">
                <div class="user-info">
                    <div class="user-avatar">
                        <img src="./images/user.svg" class="user-avatar-icon" alt="user">
                    </div>
                    <div class="user-name">CyberUser_2077</div>
                </div>
                <div class="balance-info">
                    <span class="balance-amount">1,337</span>
                    <span class="balance-currency">монет</span>
                </div>
            </div>
        </div>

        <!-- Buttons Demo -->
        <div class="demo-section">
            <h2>Кибер-кнопки</h2>
            <div style="display: flex; flex-direction: column; gap: 15px;">
                <button class="action-button purple-button">
                    Открыть ссылку
                </button>
                <button class="action-button blue-button">
                    Смотреть видео
                </button>
                <button class="action-button orange-button">
                    Открыть рекламу
                </button>
            </div>
        </div>

        <!-- Status Messages Demo -->
        <div class="demo-section">
            <h2>Статусные сообщения</h2>
            <div class="status-message success">
                Награда зачислена! Баланс: 1337 монет
            </div>
            <div class="status-message error">
                Ошибка: Недостаточно средств для вывода
            </div>
            <div class="status-message">
                Загрузка данных...
            </div>
        </div>

        <!-- Content Blocks Demo -->
        <div class="demo-section">
            <div class="earn-block">
                <h3>💰 Калькулятор вывода</h3>
                <p>Курс: 1 монета = $0.001</p>
                
                <div class="amount-input-section">
                    <label for="demo-amount">Сумма для вывода:</label>
                    <div class="input-group">
                        <input type="number" id="demo-amount" placeholder="Введите количество монет" value="1000">
                        <span class="input-suffix">монет</span>
                    </div>
                    <div class="amount-info">
                        <span id="dollar-equivalent">= $1.000</span>
                        <span class="balance-status sufficient">Достаточно средств</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Currency Tabs Demo -->
        <div class="demo-section">
            <div class="currency-tabs-container">
                <div class="currency-tabs-header">
                    <button class="currency-tab active">
                        <span class="tab-icon">⭐</span>
                        <span class="tab-name">Ethereum</span>
                        <span class="tab-symbol">ETH</span>
                    </button>
                    <button class="currency-tab">
                        <span class="tab-icon">₿</span>
                        <span class="tab-name">Bitcoin</span>
                        <span class="tab-symbol">BTC</span>
                    </button>
                    <button class="currency-tab">
                        <span class="tab-icon">💲</span>
                        <span class="tab-name">USDT</span>
                        <span class="tab-symbol">TRC20</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Demo -->
        <div class="demo-section">
            <div class="friends-block">
                <h3>Статистика рефералов</h3>
                <div class="referral-stats">
                    <div class="stat-item">
                        <div class="stat-label">Всего рефералов:</div>
                        <div class="stat-value">42</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Заработано:</div>
                        <div class="stat-value">1337</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Demo -->
        <div class="demo-section">
            <nav class="app-nav" style="position: relative; margin-top: 30px;">
                <button class="nav-button active">
                    <svg class="nav-icon"><use href="images/sprite.svg#icon-home"></use></svg>
                    <span class="nav-text">Главная</span>
                </button>
                <button class="nav-button">
                    <svg class="nav-icon"><use href="images/sprite.svg#icon-dollar"></use></svg>
                    <span class="nav-text">Заработок</span>
                </button>
                <button class="nav-button">
                    <svg class="nav-icon"><use href="images/sprite.svg#icon-friends"></use></svg>
                    <span class="nav-text">Друзья</span>
                </button>
            </nav>
        </div>

        <!-- Features Showcase -->
        <div class="demo-section">
            <h2>Особенности дизайна</h2>
            <div class="feature-showcase">
                <div class="feature-item">
                    <h3>🌟 Неоновые эффекты</h3>
                    <p>Светящиеся элементы с анимацией пульсации</p>
                </div>
                <div class="feature-item">
                    <h3>⚡ Глитч-эффекты</h3>
                    <p>Динамические искажения для ошибок</p>
                </div>
                <div class="feature-item">
                    <h3>🎨 Градиенты</h3>
                    <p>Многослойные цветовые переходы</p>
                </div>
                <div class="feature-item">
                    <h3>🚀 Анимации</h3>
                    <p>Плавные переходы и hover-эффекты</p>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="demo-section">
            <div class="friends-block">
                <h3>🎮 Как использовать</h3>
                <p>1. Замените <code>styles.css</code> на <code>cyberpunk-styles.css</code></p>
                <p>2. Подключите <code>cyberpunk-effects.js</code> для интерактивных эффектов</p>
                <p>3. Наслаждайтесь футуристическим дизайном!</p>
                
                <button class="action-button purple-button" onclick="showDemo()">
                    Показать эффекты
                </button>
            </div>
        </div>
    </div>

    <script src="cyberpunk-effects.js"></script>
    <script>
        function showDemo() {
            // Trigger some demo effects
            const buttons = document.querySelectorAll('.action-button');
            buttons.forEach((btn, index) => {
                setTimeout(() => {
                    window.CyberpunkEffects.triggerNeonFlash(btn);
                }, index * 200);
            });

            // Show success message
            setTimeout(() => {
                const successMsg = document.querySelector('.status-message.success');
                window.CyberpunkEffects.triggerNeonFlash(successMsg);
            }, 1000);

            // Glitch error message
            setTimeout(() => {
                const errorMsg = document.querySelector('.status-message.error');
                window.CyberpunkEffects.triggerGlitch(errorMsg);
            }, 1500);
        }

        // Auto-demo on load
        setTimeout(() => {
            const title = document.querySelector('.demo-title');
            window.CyberpunkEffects.triggerNeonFlash(title);
        }, 1000);
    </script>
</body>
</html>
