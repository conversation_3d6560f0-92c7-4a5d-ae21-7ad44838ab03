<?php
/**
 * Отладка cURL подключения
 */

echo "=== ОТЛАДКА cURL ===\n\n";

// Проверяем, доступен ли cURL
if (!function_exists('curl_init')) {
    echo "❌ cURL не установлен!\n";
    exit;
}

echo "✅ cURL доступен\n";

// Проверяем версию cURL
$curlVersion = curl_version();
echo "Версия cURL: {$curlVersion['version']}\n";
echo "SSL версия: {$curlVersion['ssl_version']}\n\n";

// Тестируем простое подключение
echo "Тест подключения к google.com:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://www.google.com');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Отключаем проверку SSL для теста
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: {$httpCode}\n";
echo "Error: " . ($error ?: 'Нет ошибок') . "\n";
echo "Response length: " . strlen($response) . "\n\n";

// Тестируем подключение к NOWPayments
echo "Тест подключения к NOWPayments API:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.nowpayments.io/v1/status');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Отключаем проверку SSL для теста
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

echo "HTTP Code: {$httpCode}\n";
echo "Error: " . ($error ?: 'Нет ошибок') . "\n";
echo "Response: " . substr($response, 0, 200) . "\n";
echo "Total time: {$info['total_time']} сек\n";
echo "Connect time: {$info['connect_time']} сек\n\n";

// Проверяем настройки PHP
echo "Настройки PHP:\n";
echo "allow_url_fopen: " . (ini_get('allow_url_fopen') ? 'Включено' : 'Отключено') . "\n";
echo "user_agent: " . ini_get('user_agent') . "\n";
echo "auto_detect_line_endings: " . (ini_get('auto_detect_line_endings') ? 'Включено' : 'Отключено') . "\n\n";

// Тестируем file_get_contents как альтернативу
echo "Тест file_get_contents:\n";
$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]
]);

$result = @file_get_contents('https://api.nowpayments.io/v1/status', false, $context);
if ($result !== false) {
    echo "✅ file_get_contents работает\n";
    echo "Response: " . substr($result, 0, 100) . "\n";
} else {
    echo "❌ file_get_contents не работает\n";
    $error = error_get_last();
    if ($error) {
        echo "Error: {$error['message']}\n";
    }
}

echo "\n=== ОТЛАДКА ЗАВЕРШЕНА ===\n";
?>
