<?php
// api/config.php

// !!! ТВОЙ ТОКЕН БОТА !!! (Обновленный токен)
define('TELEGRAM_BOT_TOKEN', '**********************************************'); // <-- Новый токен бота

define('USER_DATA_FILE', __DIR__ . '/user_data.json');
define('AD_VIEW_REWARD', 10);
define('REFERRAL_BONUS_PERCENT', 0.10);

// Настройки для NOWPayments API
define('NOWPAYMENTS_API_KEY', '18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7'); // Приватный ключ для payout
define('NOWPAYMENTS_PUBLIC_KEY', 'f6627c2b-98ac-4d30-90dc-c01324330248'); // Публичный ключ для estimate
define('NOWPAYMENTS_IPN_SECRET', '+dtLfBgWRcW4ybhampqglG39/zxiGgwX'); // IPN секретный ключ
define('NOWPAYMENTS_API_URL', 'https://api.nowpayments.io/v1');

// Учетные данные для получения JWT токена
define('NOWPAYMENTS_EMAIL', '<EMAIL>'); // Email от аккаунта NOWPayments
define('NOWPAYMENTS_PASSWORD', 'Yjen10,er20'); // Пароль от аккаунта NOWPayments
define('MIN_WITHDRAWAL_AMOUNT', 0); // Нет минимальной суммы для вывода (только проверка баланса)
define('MIN_BALANCE_FOR_WITHDRAWAL', 100); // Минимальный баланс для доступа к выводу средств
define('CONVERSION_RATE', 0.01); // Курс конвертации монет в USD (1 монета = 0.01 USD)
define('SHOW_FEES_TO_USER', true); // Показывать комиссии пользователю в калькуляторе

// Настройки логирования ошибок PHP для продакшена
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php-error.log');
error_reporting(E_ALL & ~E_NOTICE);
?>