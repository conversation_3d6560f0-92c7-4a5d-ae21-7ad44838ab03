<?php
/**
 * Безопасное тестирование выплат только с реальными пользовательскими адресами
 * ВНИМАНИЕ: Этот скрипт создает РЕАЛЬНЫЕ выплаты на указанные адреса!
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

// Проверяем, что скрипт запущен с правильными параметрами
if ($argc < 4) {
    echo "❌ ОШИБКА: Недостаточно параметров\n\n";
    echo "Использование:\n";
    echo "php test_user_withdrawal.php <адрес> <валюта> <сумма>\n\n";
    echo "Примеры:\n";
    echo "php test_user_withdrawal.php TYourRealTronAddress usdttrc20 10.0\n";
    echo "php test_user_withdrawal.php 1YourRealBitcoinAddress btc 0.001\n";
    echo "php test_user_withdrawal.php 0xYourRealEthereumAddress eth 0.01\n\n";
    echo "⚠️ ВНИМАНИЕ: Средства будут отправлены на указанный адрес!\n";
    echo "Убедитесь, что адрес принадлежит вам!\n";
    exit(1);
}

$userAddress = trim($argv[1]);
$currency = strtolower(trim($argv[2]));
$amount = floatval($argv[3]);

echo "🔐 БЕЗОПАСНОЕ ТЕСТИРОВАНИЕ ВЫПЛАТ\n";
echo str_repeat("=", 50) . "\n\n";

echo "⚠️ ВНИМАНИЕ: Это создаст РЕАЛЬНУЮ выплату!\n";
echo "Средства будут отправлены на указанный адрес.\n\n";

echo "📋 Параметры:\n";
echo "- Адрес: {$userAddress}\n";
echo "- Валюта: {$currency}\n";
echo "- Сумма: {$amount}\n\n";

// Валидация адреса
$isValidAddress = false;
switch ($currency) {
    case 'usdttrc20':
    case 'trx':
        $isValidAddress = preg_match('/^T[a-zA-Z0-9]{33}$/', $userAddress);
        break;
    case 'btc':
        $isValidAddress = preg_match('/^(1|3|bc1)[a-zA-Z0-9]{25,58}$/', $userAddress);
        break;
    case 'eth':
        $isValidAddress = preg_match('/^0x[a-fA-F0-9]{40}$/', $userAddress);
        break;
    case 'bnb':
        $isValidAddress = preg_match('/^(bnb1|0x)[a-zA-Z0-9]{38,40}$/', $userAddress);
        break;
    default:
        $isValidAddress = strlen($userAddress) >= 20;
}

if (!$isValidAddress) {
    echo "❌ ОШИБКА: Неверный формат адреса для валюты {$currency}\n";
    exit(1);
}

echo "✅ Адрес прошел валидацию\n\n";

// Подтверждение от пользователя
echo "🤔 Вы уверены, что хотите создать выплату?\n";
echo "Введите 'YES' для подтверждения: ";
$confirmation = trim(fgets(STDIN));

if ($confirmation !== 'YES') {
    echo "❌ Операция отменена пользователем\n";
    exit(0);
}

echo "\n🚀 Создание выплаты...\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Проверяем баланс
echo "💰 Проверяем баланс:\n";
$balance = $api->getAccountBalance();
if ($balance) {
    foreach ($balance as $curr => $data) {
        if (is_array($data) && isset($data['amount']) && $data['amount'] > 0) {
            echo "- {$curr}: {$data['amount']}\n";
        }
    }
} else {
    echo "❌ Не удалось получить баланс\n";
    exit(1);
}

// Проверяем минимальную сумму
echo "\n🔍 Проверяем минимальную сумму для {$currency}:\n";
$minAmount = $api->getMinWithdrawalAmount($currency);
if ($minAmount) {
    echo "- Минимум: {$minAmount}\n";
    echo "- Запрошено: {$amount}\n";
    
    if ($amount < $minAmount) {
        echo "❌ ОШИБКА: Сумма меньше минимальной!\n";
        echo "Увеличьте сумму до {$minAmount} или больше.\n";
        exit(1);
    }
    echo "✅ Сумма соответствует минимуму\n";
} else {
    echo "⚠️ Не удалось получить минимальную сумму\n";
}

echo "\n💳 Создание выплаты...\n";

// Создаем выплату
$result = $api->createPayoutWithFeeHandling($userAddress, $currency, $amount);

if ($result) {
    if (isset($result['error'])) {
        echo "❌ ОШИБКА: {$result['message']}\n";
        if (isset($result['code'])) {
            echo "Код: {$result['code']}\n";
        }
        if (isset($result['details'])) {
            echo "Детали:\n";
            foreach ($result['details'] as $key => $value) {
                echo "- {$key}: {$value}\n";
            }
        }
    } else {
        echo "✅ ВЫПЛАТА СОЗДАНА УСПЕШНО!\n\n";
        echo "📊 Информация о выплате:\n";
        echo "🆔 ID: {$result['id']}\n";
        
        if (isset($result['withdrawals'][0])) {
            $withdrawal = $result['withdrawals'][0];
            echo "📋 Статус: {$withdrawal['status']}\n";
            echo "💰 Сумма: {$withdrawal['amount']} {$withdrawal['currency']}\n";
            echo "📍 Адрес: {$withdrawal['address']}\n";
            echo "📅 Создана: {$withdrawal['created_at']}\n";
        }
        
        if (isset($result['fee_handling'])) {
            echo "\n💳 Информация о комиссии:\n";
            $feeInfo = $result['fee_handling'];
            echo "- Комиссия оплачена получателем: " . ($feeInfo['fee_paid_by_user'] ? 'Да' : 'Нет') . "\n";
            if (isset($feeInfo['fee_estimate'])) {
                echo "- Оценка комиссии: " . json_encode($feeInfo['fee_estimate']) . "\n";
            }
        }
        
        echo "\n🔗 Проверить статус можно в админке:\n";
        echo "http://argun-defolt.loc/api/admin/withdrawals.php\n";
        
        echo "\n📝 Выплата будет обработана NOWPayments.\n";
        echo "Средства поступят на указанный адрес после подтверждения.\n";
    }
} else {
    echo "❌ Не удалось создать выплату (нет ответа от API)\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🏁 Тестирование завершено\n";
?>
