[28-May-2025 11:34:08 UTC] requestWithdrawal INFO: Получен запрос на вывод 4 монет в usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 11:34:08 UTC] requestWithdrawal INFO: initData валидирован для user 5880288830
[28-May-2025 11:34:08 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 11:34:08 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 11:34:08 UTC] requestWithdrawal INFO: Конвертация 4 монет в USD: 0.04
[28-May-2025 11:34:08 UTC] createWithdrawalRequest INFO: Начало создания запроса на вывод для пользователя 5880288830
[28-May-2025 11:34:08 UTC] createWithdrawalRequest INFO: Параметры - Amount: 0.04 USD, Currency: usdttrc20, Address: TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.04&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:34:08 UTC] createWithdrawalRequest INFO: Конвертация 0.04 USD в 0.039936 usdttrc20
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Пробуем endpoint: https://api.nowpayments.io/v1/payout-withdrawal
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039936","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: both
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0"}
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout-withdrawal + both не сработала
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: bearer
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout-withdrawal + bearer не сработала
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: x-api-key
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout-withdrawal + x-api-key не сработала
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем endpoint: https://api.nowpayments.io/v1/payout
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039936","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: both
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.039936,"actualBalance":0}}}
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout + both не сработала
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: bearer
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout + bearer не сработала
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: x-api-key
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI HTTP INFO: Code 401, Response: {"status":false,"statusCode":401,"code":"AUTH_REQUIRED","message":"Authorization header is empty (Bearer JWTtoken is required)"}
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout + x-api-key не сработала
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI ERROR: Все endpoints и методы авторизации не сработали
[28-May-2025 11:34:10 UTC] createWithdrawalRequest ERROR: Не удалось создать выплату. Ответ API: false
[28-May-2025 11:34:10 UTC] requestWithdrawal ERROR: Не удалось создать запрос на вывод для пользователя 5880288830
[28-May-2025 11:40:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:40:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:40:43 UTC] NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами
[28-May-2025 11:40:43 UTC] NOWPaymentsAPI INFO: Минимальная сумма: 1.0E-5
[28-May-2025 11:40:43 UTC] NOWPaymentsAPI INFO: Найдена валюта btc с балансом 2.6E-5
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":1.0e-5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzI1NDcsImV4cCI6MTc0ODQzMjg0N30.wbJTZNS2br-st223KlL9EceDPjUDrOUzYrJGa0q6hwA"}
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzI1NDcsImV4cCI6MTc0ODQzMjg0N30.wbJTZNS2br-st223KlL9EceDPjUDrOUzYrJGa0q6hwA","Content-Type: application\/json"]
[28-May-2025 11:42:32 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[28-May-2025 11:42:32 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003087125","withdrawals":[{"is_request_payouts":false,"id":"5003715097","address":"**********************************","currency":"btc","amount":"0.00001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003087125","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-28T11:42:29.067Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[28-May-2025 11:42:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003087125 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzI1NDcsImV4cCI6MTc0ODQzMjg0N30.wbJTZNS2br-st223KlL9EceDPjUDrOUzYrJGa0q6hwA","Content-Type: application\/json"]
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:29 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:29 UTC] saveUserData INFO: Начало сохранения данных.
[28-May-2025 12:04:29 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[28-May-2025 12:04:29 UTC] saveUserData INFO: json_encode успешен.
[28-May-2025 12:04:29 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[28-May-2025 12:04:29 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 766
[28-May-2025 12:04:29 UTC] PHP Warning:  file_get_contents(https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/sendPhoto): Failed to open stream: HTTP request failed! HTTP/1.1 400 Bad Request
 in /var/www/html/test2/bot/config.php on line 59
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:34 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:34 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:34 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:34 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:34 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:34 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:34 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:50 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:50 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:50 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:50 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:57 UTC] getUserData INFO: Получен initData (длина: 624)
[28-May-2025 12:04:57 UTC] getUserData INFO: initData успешно валидирован для пользователя 5880288830
[28-May-2025 12:04:57 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:04:57 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:57 UTC] getUserData INFO: Данные пользователей загружены для user 5880288830.
[28-May-2025 12:04:57 UTC] db_mock INFO: Updated Telegram user data for user 5880288830
[28-May-2025 12:04:57 UTC] getUserData INFO: Детали пользователя 5880288830 получены. Баланс: 200
[28-May-2025 12:04:57 UTC] saveUserData INFO: Начало сохранения данных.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[28-May-2025 12:04:57 UTC] saveUserData INFO: json_encode успешен.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[28-May-2025 12:04:57 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 826
[28-May-2025 12:04:57 UTC] getUserData INFO: Данные пользователя 5880288830 сохранены (на случай создания).
[28-May-2025 12:04:57 UTC] getUserData INFO: Успешный ответ отправлен для user 5880288830.
[28-May-2025 12:04:57 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:04:57 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Определение языка для пользователя 5880288830
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Данные пользователя из Telegram: {"id":5880288830,"first_name":"\u0410\u043b\u044c\u0442\u0435\u0440","last_name":"\u042d\u0433\u043e","username":"alter_mega_ego","language_code":"ru","allows_write_to_pm":true,"photo_url":"https:\/\/t.me\/i\/userpic\/320\/oqepLENWEP9UvyQj4_1XP_D21AocO8M1_xVqdcAYyOik9TmM7SEWhy6lMaUFwryT.svg"}
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Автоматически определен язык для пользователя 5880288830: ru
[28-May-2025 12:04:57 UTC] saveUserData INFO: Начало сохранения данных.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[28-May-2025 12:04:57 UTC] saveUserData INFO: json_encode успешен.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[28-May-2025 12:04:57 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 852
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Язык ru сохранен для пользователя 5880288830
[28-May-2025 12:05:02 UTC] getWithdrawalHistory INFO: Получен initData (длина: 624)
[28-May-2025 12:05:02 UTC] getWithdrawalHistory INFO: initData валидирован для user 5880288830
[28-May-2025 12:05:02 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:05:02 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:05:02 UTC] getWithdrawalHistory INFO: Успешно отправлена история выплат для пользователя 5880288830 (всего: 0)
[28-May-2025 12:05:26 UTC] requestWithdrawal INFO: Получен запрос на вывод 4 монет в usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:05:26 UTC] requestWithdrawal INFO: initData валидирован для user 5880288830
[28-May-2025 12:05:26 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:05:26 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:05:26 UTC] requestWithdrawal INFO: Конвертация 4 монет в USD: 0.04
[28-May-2025 12:05:26 UTC] createWithdrawalRequest INFO: Начало создания запроса на вывод для пользователя 5880288830
[28-May-2025 12:05:26 UTC] createWithdrawalRequest INFO: Параметры - Amount: 0.04 USD, Currency: usdttrc20, Address: TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:05:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.04&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:05:31 UTC] createWithdrawalRequest INFO: Конвертация 0.04 USD в 0.039959 usdttrc20
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039959","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzM5MzEsImV4cCI6MTc0ODQzNDIzMX0.KCWm2RCXLodQgYiM8uhYgaZ3NtLK8ck1T9Vokfl96Ok"}
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzM5MzEsImV4cCI6MTc0ODQzNDIzMX0.KCWm2RCXLodQgYiM8uhYgaZ3NtLK8ck1T9Vokfl96Ok","Content-Type: application\/json"]
[28-May-2025 12:05:33 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.039959,"actualBalance":0}}}
[28-May-2025 12:05:33 UTC] NOWPaymentsAPI ERROR: Не удалось создать выплату
[28-May-2025 12:05:33 UTC] createWithdrawalRequest ERROR: Не удалось создать выплату. Ответ API: false
[28-May-2025 12:05:33 UTC] requestWithdrawal ERROR: Не удалось создать запрос на вывод для пользователя 5880288830
[28-May-2025 12:13:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":0.1,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 12:13:43 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzQ0MjAsImV4cCI6MTc0ODQzNDcyMH0.RY5sCB3Ed3RAHqcuZnPNv8B4xxIub0aMMdlhuqTps7g"}
[28-May-2025 12:13:43 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 12:13:43 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzQ0MjAsImV4cCI6MTc0ODQzNDcyMH0.RY5sCB3Ed3RAHqcuZnPNv8B4xxIub0aMMdlhuqTps7g","Content-Type: application\/json"]
[28-May-2025 12:13:45 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.1,"actualBalance":0}}}
[28-May-2025 12:13:45 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[28-May-2025 12:13:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:13:46 UTC] NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами
[28-May-2025 12:13:46 UTC] NOWPaymentsAPI INFO: Минимальная сумма: 1.0E-5
[28-May-2025 12:13:46 UTC] NOWPaymentsAPI INFO: Найдена валюта btc с балансом 2.6E-5
[28-May-2025 12:26:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Цель - 4.0E-5 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":4.0e-5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 12:26:44 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY"}
[28-May-2025 12:26:44 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 12:26:44 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.00004,"actualBalance":0}}}
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:47 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 3.6776770660058E-10 btc -> 4.0E-5 usdttrc20
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 3.6776770660058E-10 btc
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":3.677677066005816e-10,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:50 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[28-May-2025 12:26:50 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003087236","withdrawals":[{"is_request_payouts":false,"id":"5003715281","address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":"0.0000000003677677066005816","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: BTC TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","status":"REJECTED","batch_withdrawal_id":"5003087236","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-28T12:26:47.549Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[28-May-2025 12:26:50 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[28-May-2025 12:26:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003087236 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Минимальная сумма: 1.0E-5
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Найдена валюта btc с балансом 2.6E-5
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.04&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Цель - 0.039946 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039946","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.039946,"actualBalance":0}}}
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 3.6714437568071E-7 btc -> 0.039946 usdttrc20
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 3.6714437568071E-7 btc
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":3.671443756807097e-7,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:27:02 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[28-May-2025 12:27:02 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003087238","withdrawals":[{"is_request_payouts":false,"id":"5003715283","address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":"0.0000003671443756807097","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: BTC TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","status":"REJECTED","batch_withdrawal_id":"5003087238","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-28T12:26:59.042Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[28-May-2025 12:27:02 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[29-May-2025 14:05:36 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:37 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_payout_fix.php on line 46
[29-May-2025 14:05:37 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:38 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:39 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_payout_fix.php on line 88
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Автоконвертация eth -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Автоконвертация eth -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:39 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_payout_fix.php on line 139
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:06:27 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация eth -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация btc -> usdttrc20: используем тестовый адрес TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> eth: используем тестовый адрес ******************************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> ltc: используем тестовый адрес LTC1QW508D6QEJXTDG4Y5R3ZARVARY0C5XW7KV8F3T4
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> trx: используем тестовый адрес TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> usdttrc20: используем тестовый адрес TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> usdterc20: используем тестовый адрес ******************************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> bnb: используем тестовый адрес bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> doge: используем тестовый адрес DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L
[29-May-2025 14:12:06 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":1.0e-5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:12:09 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5MjYsImV4cCI6MTc0ODUyODIyNn0.SsGdjoLur57u6KPhr_W9GY-CxU0rQBVb5hPFghFAVZk"}
[29-May-2025 14:12:09 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:12:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5MjYsImV4cCI6MTc0ODUyODIyNn0.SsGdjoLur57u6KPhr_W9GY-CxU0rQBVb5hPFghFAVZk","Content-Type: application\/json"]
[29-May-2025 14:12:10 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:12:10 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096013","withdrawals":[{"is_request_payouts":false,"id":"5003725204","address":"**********************************","currency":"btc","amount":"0.00001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096013","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:12:07.803Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:12:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003096013 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5MjYsImV4cCI6MTc0ODUyODIyNn0.SsGdjoLur57u6KPhr_W9GY-CxU0rQBVb5hPFghFAVZk","Content-Type: application\/json"]
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Цель - 0.5 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.5","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ"}
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ","Content-Type: application\/json"]
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.5,"actualBalance":0}}}
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:13:23 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 4.6474531420537E-6 btc -> 0.5 usdttrc20
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 4.6474531420537E-6 btc
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":4.647453142053695e-6,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ","Content-Type: application\/json"]
[29-May-2025 14:13:25 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:13:27 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096016","withdrawals":[{"is_request_payouts":false,"id":"5003725207","address":"**********************************","currency":"btc","amount":"0.000004","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096016","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:13:22.834Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:13:27 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[29-May-2025 14:13:30 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003096016 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ","Content-Type: application\/json"]
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес **********************************
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU"}
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU","Content-Type: application\/json"]
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096105","withdrawals":[{"is_request_payouts":false,"id":"5003725327","address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: USDTTRC20 **********************************","status":"REJECTED","batch_withdrawal_id":"5003096105","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:50:57.006Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU","Content-Type: application\/json"]
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.1,"actualBalance":0}}}
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3585168622477E-7 btc -> 0.1 usdttrc20
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3585168622477E-7 btc
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:51:03 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":"0.000005","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:51:03 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU","Content-Type: application\/json"]
[29-May-2025 14:51:04 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:51:04 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096106","withdrawals":[{"is_request_payouts":false,"id":"5003725328","address":"**********************************","currency":"btc","amount":"0.000005","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096106","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:51:01.651Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:54:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:20 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":2.6e-6,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:54:22 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY"}
[29-May-2025 14:54:22 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:54:22 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY","Content-Type: application\/json"]
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096115","withdrawals":[{"is_request_payouts":false,"id":"5003725337","address":"**********************************","currency":"btc","amount":"0.000002","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096115","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:54:21.268Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес **********************************
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY","Content-Type: application\/json"]
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096116","withdrawals":[{"is_request_payouts":false,"id":"5003725338","address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: USDTTRC20 **********************************","status":"REJECTED","batch_withdrawal_id":"5003096116","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:54:23.077Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY","Content-Type: application\/json"]
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.1,"actualBalance":0}}}
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:29 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3553380479439E-7 btc -> 0.1 usdttrc20
[29-May-2025 14:54:29 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3553380479439E-7 btc
[29-May-2025 14:54:29 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 14:56:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Цель - 0.05 usdttrc20 на адрес **********************************
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Адрес ********************************** несовместим с usdttrc20, переходим к автоконвертации
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 4.6607878595798E-7 btc -> 0.05 usdttrc20
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 4.6607878595798E-7 btc
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Адрес пользователя совместим с btc, создаем выплату
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":4.660787859579783e-7,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA2MTEsImV4cCI6MTc0ODUzMDkxMX0.zO-75Gj3lWfKfoAjwKXyx1u30CajoZ_1_d_5jbV26d4"}
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA2MTEsImV4cCI6MTc0ODUzMDkxMX0.zO-75Gj3lWfKfoAjwKXyx1u30CajoZ_1_d_5jbV26d4","Content-Type: application\/json"]
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096121","withdrawals":[{"is_request_payouts":false,"id":"5003725343","address":"**********************************","currency":"btc","amount":"0","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Withdrawal amount must be greater than 0","status":"REJECTED","batch_withdrawal_id":"5003096121","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:56:52.963Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Цель - 0.05 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.05","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA2MTEsImV4cCI6MTc0ODUzMDkxMX0.zO-75Gj3lWfKfoAjwKXyx1u30CajoZ_1_d_5jbV26d4","Content-Type: application\/json"]
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.05,"actualBalance":0}}}
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:59 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 4.6569160397443E-7 btc -> 0.05 usdttrc20
[29-May-2025 14:56:59 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 4.6569160397443E-7 btc
[29-May-2025 14:56:59 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 15:00:15 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:00:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:00:16 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:00:16 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:00:16 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 15:00:16 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 15:00:16 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 15:00:17 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA4MTQsImV4cCI6MTc0ODUzMTExNH0.VFd3j5N_8iJInN-xTjO7ltp_Pl3NMGXqd6K0FS3aoqU"}
[29-May-2025 15:00:17 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 15:00:17 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA4MTQsImV4cCI6MTc0ODUzMTExNH0.VFd3j5N_8iJInN-xTjO7ltp_Pl3NMGXqd6K0FS3aoqU","Content-Type: application\/json"]
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:00:19 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA4MTQsImV4cCI6MTc0ODUzMTExNH0.VFd3j5N_8iJInN-xTjO7ltp_Pl3NMGXqd6K0FS3aoqU","Content-Type: application\/json"]
[29-May-2025 15:00:21 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 15:00:21 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:00:21 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 15:00:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:00:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:00:23 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3138320794885E-8 btc -> 0.01 usdttrc20
[29-May-2025 15:00:23 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3138320794885E-8 btc
[29-May-2025 15:00:23 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 15:01:17 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/currencies с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:01:18 UTC] PHP Fatal error:  Uncaught TypeError: strtolower(): Argument #1 ($string) must be of type string, array given in D:\OSPanel\domains\argun-defolt.loc\test_tron_compatible.php:28
Stack trace:
#0 D:\OSPanel\domains\argun-defolt.loc\test_tron_compatible.php(28): strtolower()
#1 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\test_tron_compatible.php on line 28
[29-May-2025 15:02:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:02:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA5MzYsImV4cCI6MTc0ODUzMTIzNn0.535UrHJO-TUj0j7VdK1YHmzZYlvOxyRcNphcCHT7gZQ"}
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 15:02:19 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA5MzYsImV4cCI6MTc0ODUzMTIzNn0.535UrHJO-TUj0j7VdK1YHmzZYlvOxyRcNphcCHT7gZQ","Content-Type: application\/json"]
[29-May-2025 15:02:22 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 15:02:22 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:03:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:03:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:03:19 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:03:19 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":"1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:03:19 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 15:03:19 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 15:03:19 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 15:03:20 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA5OTcsImV4cCI6MTc0ODUzMTI5N30.5pDqB9v9XwI_sIWa45HuV8-Wo7uVVSQ7c7jcdaBucxk"}
[29-May-2025 15:03:20 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 15:03:20 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA5OTcsImV4cCI6MTc0ODUzMTI5N30.5pDqB9v9XwI_sIWa45HuV8-Wo7uVVSQ7c7jcdaBucxk","Content-Type: application\/json"]
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"trx":{"requested":1,"actualBalance":0}}}
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI INFO: Цель - 1 trx на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI INFO: Адрес совместим с trx, пробуем прямую выплату
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":"1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:03:22 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA5OTcsImV4cCI6MTc0ODUzMTI5N30.5pDqB9v9XwI_sIWa45HuV8-Wo7uVVSQ7c7jcdaBucxk","Content-Type: application\/json"]
[29-May-2025 15:03:23 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"trx":{"requested":1,"actualBalance":0}}}
[29-May-2025 15:03:23 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:03:23 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 15:03:23 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:03:24 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:03:25 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 2.5700339185169E-6 btc -> 1 trx
[29-May-2025 15:03:25 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 2.5700339185169E-6 btc
[29-May-2025 15:03:25 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 15:11:13 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:11:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI INFO: Запрос с внутренней конвертацией: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","auto_conversion":true,"convert_to":"usdttrc20"}]}
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE0NzIsImV4cCI6MTc0ODUzMTc3Mn0.jxwpKFuAe9vvMk8NZgLAJFD-OPVG96gqvDBCjc1o0P0"}
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 15:11:15 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE0NzIsImV4cCI6MTc0ODUzMTc3Mn0.jxwpKFuAe9vvMk8NZgLAJFD-OPVG96gqvDBCjc1o0P0","Content-Type: application\/json"]
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:11:16 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE0NzIsImV4cCI6MTc0ODUzMTc3Mn0.jxwpKFuAe9vvMk8NZgLAJFD-OPVG96gqvDBCjc1o0P0","Content-Type: application\/json"]
[29-May-2025 15:11:18 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 15:11:18 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:11:18 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 15:11:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:11:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.2979377531676E-8 btc -> 0.01 usdttrc20
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.2979377531676E-8 btc
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:11:20 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE0NzIsImV4cCI6MTc0ODUzMTc3Mn0.jxwpKFuAe9vvMk8NZgLAJFD-OPVG96gqvDBCjc1o0P0","Content-Type: application\/json"]
[29-May-2025 15:11:21 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 15:11:21 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:11:21 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 15:11:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:11:23 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:11:23 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3136452425363E-8 btc -> 0.01 usdttrc20
[29-May-2025 15:11:23 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3136452425363E-8 btc
[29-May-2025 15:11:23 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 15:12:18 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 15:12:18 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес **********************************
[29-May-2025 15:12:18 UTC] NOWPaymentsAPI INFO: Запрос с внутренней конвертацией: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","auto_conversion":true,"convert_to":"btc"}]}
[29-May-2025 15:12:18 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 15:12:18 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 15:12:18 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE1MzYsImV4cCI6MTc0ODUzMTgzNn0.SalaKQtp_TVNgsZyD45I_KCBsAWMFWq5KfYt9Mau0Uk"}
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE1MzYsImV4cCI6MTc0ODUzMTgzNn0.SalaKQtp_TVNgsZyD45I_KCBsAWMFWq5KfYt9Mau0Uk","Content-Type: application\/json"]
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес **********************************
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI INFO: Адрес совместим с btc, пробуем прямую выплату
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:12:19 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE1MzYsImV4cCI6MTc0ODUzMTgzNn0.SalaKQtp_TVNgsZyD45I_KCBsAWMFWq5KfYt9Mau0Uk","Content-Type: application\/json"]
[29-May-2025 15:12:21 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 15:12:21 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096143","withdrawals":[{"is_request_payouts":false,"id":"5003725365","address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096143","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T15:12:18.458Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 15:12:21 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 15:12:21 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 15:12:21 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес **********************************
[29-May-2025 15:12:21 UTC] NOWPaymentsAPI ERROR: Адрес ********************************** несовместим с валютой usdttrc20
[29-May-2025 15:13:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес **********************************
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Пробуем прямую выплату в btc
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 15:13:52 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 15:13:53 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE2MzAsImV4cCI6MTc0ODUzMTkzMH0.jJ49Qj4v_5itrOGAeReIbV5UBWVbldKssQR6t5eThfY"}
[29-May-2025 15:13:53 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 15:13:53 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE2MzAsImV4cCI6MTc0ODUzMTkzMH0.jJ49Qj4v_5itrOGAeReIbV5UBWVbldKssQR6t5eThfY","Content-Type: application\/json"]
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096148","withdrawals":[{"is_request_payouts":false,"id":"5003725370","address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096148","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T15:13:52.468Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI INFO: Пробуем прямую выплату в usdttrc20
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:13:55 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE2MzAsImV4cCI6MTc0ODUzMTkzMH0.jJ49Qj4v_5itrOGAeReIbV5UBWVbldKssQR6t5eThfY","Content-Type: application\/json"]
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем внутреннюю конвертацию NOWPayments
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Запрос с внутренней конвертацией: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","auto_conversion":true,"convert_to":"usdttrc20"}]}
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE2MzAsImV4cCI6MTc0ODUzMTkzMH0.jJ49Qj4v_5itrOGAeReIbV5UBWVbldKssQR6t5eThfY","Content-Type: application\/json"]
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация также не удалась
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 15:13:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzE2MzAsImV4cCI6MTc0ODUzMTkzMH0.jJ49Qj4v_5itrOGAeReIbV5UBWVbldKssQR6t5eThfY","Content-Type: application\/json"]
[29-May-2025 15:14:00 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 15:14:00 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 15:14:00 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 15:14:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3136452425363E-8 btc -> 0.01 usdttrc20
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3136452425363E-8 btc
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес **********************************
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI ERROR: Адрес ********************************** несовместим с валютой usdttrc20
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 15:14:01 UTC] NOWPaymentsAPI ERROR: Адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 16:01:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 16:01:54 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 16:01:55 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1MTIsImV4cCI6MTc0ODUzNDgxMn0.W0zAiuIlsm-ypa9FACiiCWD4v4FZCB0LzsmgGTPoAHQ"}
[29-May-2025 16:01:55 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 16:01:55 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1MTIsImV4cCI6MTc0ODUzNDgxMn0.W0zAiuIlsm-ypa9FACiiCWD4v4FZCB0LzsmgGTPoAHQ","Content-Type: application\/json"]
[29-May-2025 16:01:56 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:01:56 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:01:56 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:01:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:01:57 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:01:57 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:01:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:01:59 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.318282085651E-8 btc -> 0.036142 trx (эквивалент 0.01 usdttrc20)
[29-May-2025 16:01:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:01:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:02:00 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3429834374058E-8 btc -> 0.010000 usdttrc20 (эквивалент 0.01 usdttrc20)
[29-May-2025 16:02:00 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3429834374058E-8 btc -> 0.010000 usdttrc20
[29-May-2025 16:02:00 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:02:00 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.010000","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:02:00 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1MTIsImV4cCI6MTc0ODUzNDgxMn0.W0zAiuIlsm-ypa9FACiiCWD4v4FZCB0LzsmgGTPoAHQ","Content-Type: application\/json"]
[29-May-2025 16:02:01 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:02:01 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:02:02 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:02:02 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_smart_conversion.php on line 149
[29-May-2025 16:03:06 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:03:08 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Пробуем прямую выплату в usdttrc20
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1ODYsImV4cCI6MTc0ODUzNDg4Nn0.KrkvtA1vIg6m6fV8a1r2jzX4bNDPjP6ykGPXiUog8XU"}
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 16:03:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1ODYsImV4cCI6MTc0ODUzNDg4Nn0.KrkvtA1vIg6m6fV8a1r2jzX4bNDPjP6ykGPXiUog8XU","Content-Type: application\/json"]
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем внутреннюю конвертацию NOWPayments
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Запрос с внутренней конвертацией: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","auto_conversion":true,"convert_to":"usdttrc20"}]}
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1ODYsImV4cCI6MTc0ODUzNDg4Nn0.KrkvtA1vIg6m6fV8a1r2jzX4bNDPjP6ykGPXiUog8XU","Content-Type: application\/json"]
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация также не удалась
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:03:11 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1ODYsImV4cCI6MTc0ODUzNDg4Nn0.KrkvtA1vIg6m6fV8a1r2jzX4bNDPjP6ykGPXiUog8XU","Content-Type: application\/json"]
[29-May-2025 16:03:13 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:03:13 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:03:13 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:03:13 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:03:13 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:03:13 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:03:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:03:15 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.318282085651E-8 btc -> 0.036142 trx (эквивалент 0.01 usdttrc20)
[29-May-2025 16:03:15 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:03:15 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:03:16 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3453375392145E-8 btc -> 0.010000 usdttrc20 (эквивалент 0.01 usdttrc20)
[29-May-2025 16:03:16 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3453375392145E-8 btc -> 0.010000 usdttrc20
[29-May-2025 16:03:16 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:03:16 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.010000","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:03:16 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ1ODYsImV4cCI6MTc0ODUzNDg4Nn0.KrkvtA1vIg6m6fV8a1r2jzX4bNDPjP6ykGPXiUog8XU","Content-Type: application\/json"]
[29-May-2025 16:03:17 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:03:17 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:03:17 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:03:17 UTC] PHP Fatal error:  Uncaught Error: Cannot access private property NOWPaymentsAPI::$apiUrl in D:\OSPanel\domains\argun-defolt.loc\test_final_conversion.php:82
Stack trace:
#0 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\test_final_conversion.php on line 82
[29-May-2025 16:04:13 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:04:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/currencies с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:04:15 UTC] PHP Fatal error:  Uncaught TypeError: strtolower(): Argument #1 ($string) must be of type string, array given in D:\OSPanel\domains\argun-defolt.loc\test_exchange_conversion.php:40
Stack trace:
#0 D:\OSPanel\domains\argun-defolt.loc\test_exchange_conversion.php(40): strtolower()
#1 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\test_exchange_conversion.php on line 40
[29-May-2025 16:06:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес **********************************
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Пробуем прямую выплату в btc
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ3OTgsImV4cCI6MTc0ODUzNTA5OH0.Jk_sDD5NtoWvE_fa1mrD9PMtn3-r_90A07rLqWOItmA"}
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 16:06:40 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ3OTgsImV4cCI6MTc0ODUzNTA5OH0.Jk_sDD5NtoWvE_fa1mrD9PMtn3-r_90A07rLqWOItmA","Content-Type: application\/json"]
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096320","withdrawals":[{"is_request_payouts":false,"id":"5003725565","address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096320","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T16:06:40.050Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI INFO: Пробуем прямую выплату в usdttrc20
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:06:43 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ3OTgsImV4cCI6MTc0ODUzNTA5OH0.Jk_sDD5NtoWvE_fa1mrD9PMtn3-r_90A07rLqWOItmA","Content-Type: application\/json"]
[29-May-2025 16:06:44 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:06:44 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:06:44 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем внутреннюю конвертацию NOWPayments
[29-May-2025 16:06:44 UTC] NOWPaymentsAPI INFO: Запрос с внутренней конвертацией: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","auto_conversion":true,"convert_to":"usdttrc20"}]}
[29-May-2025 16:06:44 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ3OTgsImV4cCI6MTc0ODUzNTA5OH0.Jk_sDD5NtoWvE_fa1mrD9PMtn3-r_90A07rLqWOItmA","Content-Type: application\/json"]
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация также не удалась
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:06:45 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ3OTgsImV4cCI6MTc0ODUzNTA5OH0.Jk_sDD5NtoWvE_fa1mrD9PMtn3-r_90A07rLqWOItmA","Content-Type: application\/json"]
[29-May-2025 16:06:46 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:06:46 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:06:46 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:06:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:06:47 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:06:47 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:06:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:06:49 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3294364543511E-8 btc -> 0.036142 trx (эквивалент 0.01 usdttrc20)
[29-May-2025 16:06:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:06:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:06:50 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3633430880161E-8 btc -> 0.010000 usdttrc20 (эквивалент 0.01 usdttrc20)
[29-May-2025 16:06:50 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3633430880161E-8 btc -> 0.010000 usdttrc20
[29-May-2025 16:06:50 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:06:50 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.010000","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:06:50 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzQ3OTgsImV4cCI6MTc0ODUzNTA5OH0.Jk_sDD5NtoWvE_fa1mrD9PMtn3-r_90A07rLqWOItmA","Content-Type: application\/json"]
[29-May-2025 16:06:51 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:06:51 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:06:51 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:06:51 UTC] NOWPaymentsAPI INFO: Создание выплаты с внутренней конвертацией NOWPayments
[29-May-2025 16:06:51 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:06:51 UTC] NOWPaymentsAPI ERROR: Адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 16:17:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:45 UTC] NOWPaymentsAPI INFO: Поиск лучшей автоконвертации
[29-May-2025 16:17:45 UTC] NOWPaymentsAPI INFO: Проверяем btc: 2.6E-5
[29-May-2025 16:17:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:47 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.335199224018E-8 btc -> 0.036035 trx
[29-May-2025 16:17:47 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:48 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.3879761689836E-8 btc -> 0.01 usdttrc20
[29-May-2025 16:17:48 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция: {"source_currency":"btc","source_amount":9.387976168983571e-8,"target_currency":"usdttrc20","target_amount":"0.01","original_target_currency":"usdttrc20","original_target_amount":"0.01","priority":1,"efficiency":106519.23076923077}
[29-May-2025 16:17:48 UTC] NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией
[29-May-2025 16:17:48 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:17:48 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса: trx, usdttrc20
[29-May-2025 16:17:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:49 UTC] NOWPaymentsAPI INFO: Поиск лучшей автоконвертации
[29-May-2025 16:17:49 UTC] NOWPaymentsAPI INFO: Проверяем btc: 2.6E-5
[29-May-2025 16:17:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:50 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.3436516304194E-8 btc -> 0.036035 trx
[29-May-2025 16:17:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.3879761689836E-8 btc -> 0.01 usdttrc20
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция: {"source_currency":"btc","source_amount":9.387976168983571e-8,"target_currency":"usdttrc20","target_amount":"0.01","original_target_currency":"usdttrc20","original_target_amount":"0.01","priority":1,"efficiency":106519.23076923077}
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Создаем выплату с автоконвертацией: {"source_currency":"btc","source_amount":9.387976168983571e-8,"target_currency":"usdttrc20","target_amount":"0.01","original_target_currency":"usdttrc20","original_target_amount":"0.01","priority":1,"efficiency":106519.23076923077}
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU0NjksImV4cCI6MTc0ODUzNTc2OX0.TIVLcOsbk0i_3kBWB8hlVMsMBe_hDiA_WL29ZbqCBDA"}
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 16:17:51 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU0NjksImV4cCI6MTc0ODUzNTc2OX0.TIVLcOsbk0i_3kBWB8hlVMsMBe_hDiA_WL29ZbqCBDA","Content-Type: application\/json"]
[29-May-2025 16:17:53 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:17:53 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:17:53 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:17:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:55 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:17:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:56 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3436516304194E-8 btc -> 0.036035 trx (эквивалент 0.01 usdttrc20)
[29-May-2025 16:17:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:17:57 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3760931262419E-8 btc -> 0.010000 usdttrc20 (эквивалент 0.01 usdttrc20)
[29-May-2025 16:17:57 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3760931262419E-8 btc -> 0.010000 usdttrc20
[29-May-2025 16:17:57 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:17:57 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.010000","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:17:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU0NjksImV4cCI6MTc0ODUzNTc2OX0.TIVLcOsbk0i_3kBWB8hlVMsMBe_hDiA_WL29ZbqCBDA","Content-Type: application\/json"]
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:17:58 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU0NjksImV4cCI6MTc0ODUzNTc2OX0.TIVLcOsbk0i_3kBWB8hlVMsMBe_hDiA_WL29ZbqCBDA","Content-Type: application\/json"]
[29-May-2025 16:18:00 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:18:00 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:18:00 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:18:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:18:01 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:18:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:18:02 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:18:02 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3436516304194E-8 btc -> 0.036035 trx (эквивалент 0.01 usdttrc20)
[29-May-2025 16:18:02 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:18:03 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:18:03 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3879761689836E-8 btc -> 0.010000 usdttrc20 (эквивалент 0.01 usdttrc20)
[29-May-2025 16:18:03 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3879761689836E-8 btc -> 0.010000 usdttrc20
[29-May-2025 16:18:03 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:18:03 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.010000","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:18:03 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU0NjksImV4cCI6MTc0ODUzNTc2OX0.TIVLcOsbk0i_3kBWB8hlVMsMBe_hDiA_WL29ZbqCBDA","Content-Type: application\/json"]
[29-May-2025 16:18:06 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:18:06 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:18:06 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:18:06 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_full_auto_conversion.php on line 198
[29-May-2025 16:19:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:19:15 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:19:15 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:19:16 UTC] PHP Fatal error:  Uncaught Error: Call to private method NOWPaymentsAPI::makeRequest() from global scope in D:\OSPanel\domains\argun-defolt.loc\test_nowpayments_exchange.php:69
Stack trace:
#0 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\test_nowpayments_exchange.php on line 69
[29-May-2025 16:20:13 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg"}
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 16:20:14 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:16 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.001,"actualBalance":0}}}
[29-May-2025 16:20:16 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:16 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:16 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:16 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:18 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:20:18 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:18 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:18 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:18 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:19 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.1,"actualBalance":0}}}
[29-May-2025 16:20:19 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:19 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:19 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:19 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:21 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":1,"actualBalance":0}}}
[29-May-2025 16:20:21 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:21 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:21 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:21 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:22 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"trx":{"requested":0.1,"actualBalance":0}}}
[29-May-2025 16:20:22 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:22 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:22 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":"1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:22 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:24 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"trx":{"requested":1,"actualBalance":0}}}
[29-May-2025 16:20:24 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:24 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:24 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":"10","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:24 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:26 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"trx":{"requested":10,"actualBalance":0}}}
[29-May-2025 16:20:26 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:26 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:20:26 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":"100","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:20:26 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU2MTIsImV4cCI6MTc0ODUzNTkxMn0.ypXjM1IZb16ekKu7VdH63qekXZ9SE7TI24MyKjnhGfg","Content-Type: application\/json"]
[29-May-2025 16:20:27 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"trx":{"requested":100,"actualBalance":0}}}
[29-May-2025 16:20:27 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:20:27 UTC] PHP Fatal error:  Uncaught Error: Cannot access private property NOWPaymentsAPI::$apiUrl in D:\OSPanel\domains\argun-defolt.loc\test_minimal_payout.php:106
Stack trace:
#0 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\test_minimal_payout.php on line 106
[29-May-2025 16:22:32 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес **********************************
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса: btc
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Поиск лучшей автоконвертации
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Проверяем btc: 2.6E-5
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Найдена опция: 1.0E-6 btc -> 0.000001 btc
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция: {"source_currency":"btc","source_amount":1.0e-6,"target_currency":"btc","target_amount":"0.000001","original_target_currency":"btc","original_target_amount":"0.000001","priority":3,"efficiency":1}
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Создаем выплату с автоконвертацией: {"source_currency":"btc","source_amount":1.0e-6,"target_currency":"btc","target_amount":"0.000001","original_target_currency":"btc","original_target_amount":"0.000001","priority":3,"efficiency":1}
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес **********************************
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Адрес совместим с btc, пробуем прямую выплату
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 16:22:34 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 16:22:35 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y"}
[29-May-2025 16:22:35 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 16:22:35 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:22:37 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 16:22:37 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096363","withdrawals":[{"is_request_payouts":false,"id":"5003725608","address":"**********************************","currency":"btc","amount":"0.000001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096363","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T16:22:34.140Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 16:22:37 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 16:22:37 UTC] NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией
[29-May-2025 16:22:37 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:22:37 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса: trx, usdttrc20
[29-May-2025 16:22:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:38 UTC] NOWPaymentsAPI INFO: Поиск лучшей автоконвертации
[29-May-2025 16:22:38 UTC] NOWPaymentsAPI INFO: Проверяем btc: 2.6E-5
[29-May-2025 16:22:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000001&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:39 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.9980572873322E-7 btc -> 0.386379 trx
[29-May-2025 16:22:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000001&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Найдена опция: 1.0000039622634E-6 btc -> 0.106777 usdttrc20
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция: {"source_currency":"btc","source_amount":1.0000039622634032e-6,"target_currency":"usdttrc20","target_amount":"0.106777","original_target_currency":"btc","original_target_amount":"0.000001","priority":1,"efficiency":106776.57692307692}
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Создаем выплату с автоконвертацией: {"source_currency":"btc","source_amount":1.0000039622634032e-6,"target_currency":"usdttrc20","target_amount":"0.106777","original_target_currency":"btc","original_target_amount":"0.000001","priority":1,"efficiency":106776.57692307692}
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Цель - 0.106777 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.106777","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:22:40 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:22:42 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.106777,"actualBalance":0}}}
[29-May-2025 16:22:42 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:22:42 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:22:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:43 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:22:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.106777&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:44 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.9553613992945E-7 btc -> 0.384729 trx (эквивалент 0.106777 usdttrc20)
[29-May-2025 16:22:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.106777&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:46 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.9965468332589E-7 btc -> 0.106777 usdttrc20 (эквивалент 0.106777 usdttrc20)
[29-May-2025 16:22:46 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.9965468332589E-7 btc -> 0.106777 usdttrc20
[29-May-2025 16:22:46 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:22:46 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.106777","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:22:46 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.106777,"actualBalance":0}}}
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI INFO: Адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с btc, переходим к автоконвертации
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:22:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:49 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:22:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000001&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:51 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.9980572873322E-7 btc -> 0.386379 trx (эквивалент 0.000001 btc)
[29-May-2025 16:22:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000001&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:52 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 1.0000010802399E-6 btc -> 0.106814 usdttrc20 (эквивалент 0.000001 btc)
[29-May-2025 16:22:52 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 1.0000010802399E-6 btc -> 0.106814 usdttrc20
[29-May-2025 16:22:52 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:22:52 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.106814","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:22:52 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:22:53 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.106814,"actualBalance":0}}}
[29-May-2025 16:22:53 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:22:53 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:22:53 UTC] NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией
[29-May-2025 16:22:53 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:22:53 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса: trx, usdttrc20
[29-May-2025 16:22:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:54 UTC] NOWPaymentsAPI INFO: Поиск лучшей автоконвертации
[29-May-2025 16:22:54 UTC] NOWPaymentsAPI INFO: Проверяем btc: 2.6E-5
[29-May-2025 16:22:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.326334525169E-8 btc -> 0.036042 trx
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.362078755967E-8 btc -> 0.01 usdttrc20
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция: {"source_currency":"btc","source_amount":9.362078755966975e-8,"target_currency":"usdttrc20","target_amount":"0.01","original_target_currency":"usdttrc20","original_target_amount":"0.01","priority":1,"efficiency":106813.88461538462}
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Создаем выплату с автоконвертацией: {"source_currency":"btc","source_amount":9.362078755966975e-8,"target_currency":"usdttrc20","target_amount":"0.01","original_target_currency":"usdttrc20","original_target_amount":"0.01","priority":1,"efficiency":106813.88461538462}
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:22:56 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:22:58 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:22:58 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:22:58 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:22:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:59 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:22:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:22:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:00 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3234881326331E-8 btc -> 0.036031 trx (эквивалент 0.01 usdttrc20)
[29-May-2025 16:23:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:01 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.362078755967E-8 btc -> 0.010000 usdttrc20 (эквивалент 0.01 usdttrc20)
[29-May-2025 16:23:01 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.362078755967E-8 btc -> 0.010000 usdttrc20
[29-May-2025 16:23:01 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:23:01 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.010000","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:23:01 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI INFO: Цель - 0.01 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.01","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:23:04 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:23:05 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:23:05 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:23:05 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 16:23:05 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:06 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK: trx, usdttrc20
[29-May-2025 16:23:06 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:07 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:07 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.325309981452E-8 btc -> 0.036031 trx (эквивалент 0.01 usdttrc20)
[29-May-2025 16:23:07 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:08 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 16:23:09 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3653498624554E-8 btc -> 0.010000 usdttrc20 (эквивалент 0.01 usdttrc20)
[29-May-2025 16:23:09 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3653498624554E-8 btc -> 0.010000 usdttrc20
[29-May-2025 16:23:09 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 16:23:09 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.010000","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 16:23:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzU3NTIsImV4cCI6MTc0ODUzNjA1Mn0.W_WzOCFYwtoPoZ-sOlkBbHMOz-5OqEdFihIrplG-w5Y","Content-Type: application\/json"]
[29-May-2025 16:23:10 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.01,"actualBalance":0}}}
[29-May-2025 16:23:10 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 16:23:10 UTC] NOWPaymentsAPI ERROR: Автоконвертация также не удалась
[29-May-2025 16:25:04 UTC] NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией
[29-May-2025 16:25:04 UTC] NOWPaymentsAPI INFO: Цель - 0.000001 btc на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 16:25:04 UTC] NOWPaymentsAPI ERROR: Адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[30-May-2025 15:09:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:09:23 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\api\quick_test.php on line 20
[30-May-2025 15:09:24 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\api\quick_test.php on line 20
[30-May-2025 15:09:24 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\api\quick_test.php on line 20
[30-May-2025 15:09:24 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\api\quick_test.php on line 20
[30-May-2025 15:09:24 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:09:24 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:09:24 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:09:25 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU"}
[30-May-2025 15:09:25 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:09:25 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:25 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:09:25 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ETH с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:26 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:09:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/USDT с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:27 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:09:27 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/LTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:28 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:09:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/TRX с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:29 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:09:29 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:09:29 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:29 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:09:29 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:30 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:09:30 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"**********************************","currency":"BTC","amount":1.0e-5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","fee_paid_by_user":true}]}
[30-May-2025 15:09:30 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc3NjQsImV4cCI6MTc0ODYxODA2NH0.88RhRy3nZCo3YQyPh4mfqdKo7kKei2iaso-XMLuE-kU","Content-Type: application\/json"]
[30-May-2025 15:09:31 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[30-May-2025 15:10:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:10:59 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:10:59 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:10:59 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:11:00 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s"}
[30-May-2025 15:11:00 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:11:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:00 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:11:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ETH с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:01 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:11:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/USDT с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:02 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:11:02 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/LTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:02 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:11:02 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/TRX с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:03 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:11:03 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:03 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:11:03 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:04 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:11:04 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:11:04 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:04 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:11:04 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:05 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:11:05 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"**********************************","currency":"BTC","amount":0.0001,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","fee_paid_by_user":true}]}
[30-May-2025 15:11:05 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc4NTksImV4cCI6MTc0ODYxODE1OX0.Q_DNr9h7YF6LWp1YgDZlIuS516BgkJOLXJdTtzNup6s","Content-Type: application\/json"]
[30-May-2025 15:11:06 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[30-May-2025 15:12:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:12:45 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:12:45 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:12:45 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:12:46 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo"}
[30-May-2025 15:12:46 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:12:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:49 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:12:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:12:50 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:50 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:12:50 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:51 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:12:51 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:12:52 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:12:52 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:12:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:53 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:12:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:12:54 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:55 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:12:55 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:55 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:12:55 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:12:56 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:12:56 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE","currency":"usdttrc20","amount":0.5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","fee_paid_by_user":true}]}
[30-May-2025 15:12:56 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTc5NjUsImV4cCI6MTc0ODYxODI2NX0.aOudT4Pag8MUM9LKCK-uDhGvsXOOMjzCjX8siWEnmHo","Content-Type: application\/json"]
[30-May-2025 15:12:57 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[30-May-2025 15:13:36 UTC] PHP Fatal error:  Uncaught Error: Call to private method NOWPaymentsAPI::makeRequest() from global scope in D:\OSPanel\domains\argun-defolt.loc\api\test_simple_payout.php:38
Stack trace:
#0 {main}
  thrown in D:\OSPanel\domains\argun-defolt.loc\api\test_simple_payout.php on line 38
[30-May-2025 15:14:35 UTC] PHP Warning:  Undefined array key "response" in D:\OSPanel\domains\argun-defolt.loc\api\test_api_connection.php on line 51
[30-May-2025 15:14:35 UTC] PHP Warning:  Undefined array key "response" in D:\OSPanel\domains\argun-defolt.loc\api\test_api_connection.php on line 61
[30-May-2025 15:14:35 UTC] PHP Deprecated:  substr(): Passing null to parameter #1 ($string) of type string is deprecated in D:\OSPanel\domains\argun-defolt.loc\api\test_api_connection.php on line 61
[30-May-2025 15:14:36 UTC] PHP Warning:  Undefined array key "response" in D:\OSPanel\domains\argun-defolt.loc\api\test_api_connection.php on line 67
[30-May-2025 15:14:36 UTC] PHP Warning:  Undefined array key "response" in D:\OSPanel\domains\argun-defolt.loc\api\test_api_connection.php on line 73
[30-May-2025 15:17:32 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:32 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:17:32 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:17:32 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:17:33 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8"}
[30-May-2025 15:17:33 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:17:33 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:34 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:17:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:35 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ETH с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:36 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ETH с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:37 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:17:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ETH с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/USDT с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/USDT с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:39 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:17:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/USDT с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:40 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/LTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/LTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:42 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:17:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/LTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/TRX с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/TRX с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:44 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:17:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/TRX с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:46 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:17:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:47 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:48 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:17:48 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:49 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:17:49 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:49 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:17:49 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:17:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:51 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:17:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/BTC с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:52 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:52 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:17:52 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:53 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:17:53 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:17:53 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:17:53 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"**********************************","currency":"BTC","amount":0.0001,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","fee_paid_by_user":true}]}
[30-May-2025 15:17:53 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgyNTIsImV4cCI6MTc0ODYxODU1Mn0.5xO_-OUnLTrEM5F2xQo7fHY0oWd0WTL2q_cJxl9BmS8","Content-Type: application\/json"]
[30-May-2025 15:17:54 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[30-May-2025 15:19:09 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:19:10 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:19:10 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:19:10 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:19:10 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:19:11 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgzNTAsImV4cCI6MTc0ODYxODY1MH0.Vx4DqgfhUUcelE-QvmpRQ6LLkxzSDqwDstJtLhudRg0"}
[30-May-2025 15:19:11 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:19:11 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgzNTAsImV4cCI6MTc0ODYxODY1MH0.Vx4DqgfhUUcelE-QvmpRQ6LLkxzSDqwDstJtLhudRg0","Content-Type: application\/json"]
[30-May-2025 15:19:12 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgzNTAsImV4cCI6MTc0ODYxODY1MH0.Vx4DqgfhUUcelE-QvmpRQ6LLkxzSDqwDstJtLhudRg0","Content-Type: application\/json"]
[30-May-2025 15:19:12 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:19:12 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:19:13 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgzNTAsImV4cCI6MTc0ODYxODY1MH0.Vx4DqgfhUUcelE-QvmpRQ6LLkxzSDqwDstJtLhudRg0","Content-Type: application\/json"]
[30-May-2025 15:19:13 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:19:13 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgzNTAsImV4cCI6MTc0ODYxODY1MH0.Vx4DqgfhUUcelE-QvmpRQ6LLkxzSDqwDstJtLhudRg0","Content-Type: application\/json"]
[30-May-2025 15:19:14 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:19:14 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:19:15 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:19:15 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":5.0e-6,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","fee_paid_by_user":true}]}
[30-May-2025 15:19:15 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MTgzNTAsImV4cCI6MTc0ODYxODY1MH0.Vx4DqgfhUUcelE-QvmpRQ6LLkxzSDqwDstJtLhudRg0","Content-Type: application\/json"]
[30-May-2025 15:19:15 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals[0] does not match any of the allowed types"}
[30-May-2025 15:51:19 UTC] PHP Warning:  Undefined property: TestAPI::$apiUrl in D:\OSPanel\domains\argun-defolt.loc\api\test_payout_format.php on line 64
[30-May-2025 15:51:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:52:35 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:52:36 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:52:36 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:52:36 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:52:36 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:52:36 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjAzNTUsImV4cCI6MTc0ODYyMDY1NX0.Hfg7bSPLJaysIcg85Ekb7oFVM6g53q0qBN-CYRjcN0M"}
[30-May-2025 15:52:36 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:52:36 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjAzNTUsImV4cCI6MTc0ODYyMDY1NX0.Hfg7bSPLJaysIcg85Ekb7oFVM6g53q0qBN-CYRjcN0M","Content-Type: application\/json"]
[30-May-2025 15:52:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjAzNTUsImV4cCI6MTc0ODYyMDY1NX0.Hfg7bSPLJaysIcg85Ekb7oFVM6g53q0qBN-CYRjcN0M","Content-Type: application\/json"]
[30-May-2025 15:52:38 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:52:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:52:39 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjAzNTUsImV4cCI6MTc0ODYyMDY1NX0.Hfg7bSPLJaysIcg85Ekb7oFVM6g53q0qBN-CYRjcN0M","Content-Type: application\/json"]
[30-May-2025 15:52:39 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:52:39 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjAzNTUsImV4cCI6MTc0ODYyMDY1NX0.Hfg7bSPLJaysIcg85Ekb7oFVM6g53q0qBN-CYRjcN0M","Content-Type: application\/json"]
[30-May-2025 15:52:40 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:52:40 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:52:41 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:52:41 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":0.01,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","fee_paid_by_user":true}
[30-May-2025 15:52:41 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjAzNTUsImV4cCI6MTc0ODYyMDY1NX0.Hfg7bSPLJaysIcg85Ekb7oFVM6g53q0qBN-CYRjcN0M","Content-Type: application\/json"]
[30-May-2025 15:52:42 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"status":false,"statusCode":400,"code":"INVALID_REQUEST_PARAMS","message":"withdrawals is required"}
[30-May-2025 15:53:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:53:29 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:53:29 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:53:29 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:53:29 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:53:30 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA0MDgsImV4cCI6MTc0ODYyMDcwOH0.bnaTgsxWR8fMaUpwVr-goQMHRf9duAeHHFL5k0jOihg"}
[30-May-2025 15:53:30 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:53:30 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA0MDgsImV4cCI6MTc0ODYyMDcwOH0.bnaTgsxWR8fMaUpwVr-goQMHRf9duAeHHFL5k0jOihg","Content-Type: application\/json"]
[30-May-2025 15:53:30 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA0MDgsImV4cCI6MTc0ODYyMDcwOH0.bnaTgsxWR8fMaUpwVr-goQMHRf9duAeHHFL5k0jOihg","Content-Type: application\/json"]
[30-May-2025 15:53:31 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:53:31 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:53:32 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA0MDgsImV4cCI6MTc0ODYyMDcwOH0.bnaTgsxWR8fMaUpwVr-goQMHRf9duAeHHFL5k0jOihg","Content-Type: application\/json"]
[30-May-2025 15:53:32 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:53:32 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA0MDgsImV4cCI6MTc0ODYyMDcwOH0.bnaTgsxWR8fMaUpwVr-goQMHRf9duAeHHFL5k0jOihg","Content-Type: application\/json"]
[30-May-2025 15:53:33 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:53:33 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:53:34 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:53:34 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":0.01}]}
[30-May-2025 15:53:34 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA0MDgsImV4cCI6MTc0ODYyMDcwOH0.bnaTgsxWR8fMaUpwVr-goQMHRf9duAeHHFL5k0jOihg","Content-Type: application\/json"]
[30-May-2025 15:54:27 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[30-May-2025 15:54:27 UTC] loadUserData INFO: Данные успешно загружены.
[30-May-2025 15:56:25 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[30-May-2025 15:56:25 UTC] loadUserData INFO: Данные успешно загружены.
[30-May-2025 18:56:25 Europe/Moscow] requestWithdrawal INFO: Получен запрос на вывод 50 монет в usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[30-May-2025 18:56:25 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[30-May-2025 18:56:25 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[30-May-2025 15:57:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:57:42 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 15:57:42 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 15:57:42 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 15:57:42 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 15:57:43 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA2NjIsImV4cCI6MTc0ODYyMDk2Mn0.AwMcFXQakCNEnDbWz7EAGK058iqHi9rnhDhYG7w6DuI"}
[30-May-2025 15:57:43 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 15:57:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA2NjIsImV4cCI6MTc0ODYyMDk2Mn0.AwMcFXQakCNEnDbWz7EAGK058iqHi9rnhDhYG7w6DuI","Content-Type: application\/json"]
[30-May-2025 15:57:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA2NjIsImV4cCI6MTc0ODYyMDk2Mn0.AwMcFXQakCNEnDbWz7EAGK058iqHi9rnhDhYG7w6DuI","Content-Type: application\/json"]
[30-May-2025 15:57:45 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 15:57:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:57:46 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA2NjIsImV4cCI6MTc0ODYyMDk2Mn0.AwMcFXQakCNEnDbWz7EAGK058iqHi9rnhDhYG7w6DuI","Content-Type: application\/json"]
[30-May-2025 15:57:46 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:57:46 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA2NjIsImV4cCI6MTc0ODYyMDk2Mn0.AwMcFXQakCNEnDbWz7EAGK058iqHi9rnhDhYG7w6DuI","Content-Type: application\/json"]
[30-May-2025 15:57:47 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:57:47 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 15:57:47 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 15:57:47 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":0.02}]}
[30-May-2025 15:57:47 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjA2NjIsImV4cCI6MTc0ODYyMDk2Mn0.AwMcFXQakCNEnDbWz7EAGK058iqHi9rnhDhYG7w6DuI","Content-Type: application\/json"]
[30-May-2025 18:58:36 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[30-May-2025 18:58:36 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[30-May-2025 18:58:36 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:58:37 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:58:37 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.058&currency_from=usdttrc20&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:58:38 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000119&currency_from=eth&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:58:39 Europe/Moscow] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.149006&currency_from=trx&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:58:48 Europe/Moscow] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[30-May-2025 18:58:48 Europe/Moscow] loadUserData INFO: Данные успешно загружены.
[30-May-2025 16:05:42 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 16:05:42 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 16:05:42 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 16:05:43 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI"}
[30-May-2025 16:05:43 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 16:05:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003737622 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003737455 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:05:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:46 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:05:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:05:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:49 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:05:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:05:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:51 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:05:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:05:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjExNDIsImV4cCI6MTc0ODYyMTQ0Mn0.uwzhG3Enfhg5Pg5ByPEHpgffoy8aQrMuSlaSiqTbGbI","Content-Type: application\/json"]
[30-May-2025 16:05:54 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:05:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:06:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:06:52 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:06:52 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 16:06:52 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 16:06:52 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 16:06:52 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw"}
[30-May-2025 16:06:52 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 16:06:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:06:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:06:54 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:06:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:06:55 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:06:55 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:06:55 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:06:56 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:06:56 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:06:57 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:06:57 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":1}]}
[30-May-2025 16:06:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:06:58 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:06:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:06:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:07:00 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:07:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:07:01 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:07:02 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:07:02 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:07:03 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:07:03 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:07:03 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:07:03 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":5.0e-6}]}
[30-May-2025 16:07:03 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEyMTEsImV4cCI6MTc0ODYyMTUxMX0.IXqtNV4qA65ZYIL0ftlgMR6a-BfOsWhijmga_EtD2Iw","Content-Type: application\/json"]
[30-May-2025 16:09:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:09:45 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 16:09:45 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 16:09:45 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 16:09:45 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA"}
[30-May-2025 16:09:45 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 16:09:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:46 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:09:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:09:47 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[30-May-2025 16:09:47 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:49 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:09:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:09:50 UTC] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[30-May-2025 16:09:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:51 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:09:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:09:52 UTC] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.001
[30-May-2025 16:09:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:53 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:09:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:09:54 UTC] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[30-May-2025 16:09:54 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:09:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:55 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:09:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:09:56 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[30-May-2025 16:09:56 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:09:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:09:59 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:09:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:09:59 UTC] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[30-May-2025 16:09:59 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:10:00 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:10:00 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:10:01 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:10:01 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:10:01 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:10:01 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"trx","amount":1}]}
[30-May-2025 16:10:01 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:10:05 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"trx":{"requested":1,"actualBalance":0.149006}}}
[30-May-2025 16:10:05 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:10:05 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:10:06 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:10:06 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:10:06 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:10:07 UTC] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[30-May-2025 16:10:07 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:10:08 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:10:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:10:08 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:10:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:10:09 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:10:09 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":5.0e-6}]}
[30-May-2025 16:10:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjEzODQsImV4cCI6MTc0ODYyMTY4NH0.pWqCT06-zujex7DFUsPIkEqSjuvugXTLh_GDHRQImmA","Content-Type: application\/json"]
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: **********************************
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: ******************************************
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 16:59:32 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 16:59:33 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjQzNzIsImV4cCI6MTc0ODYyNDY3Mn0.GgqNXEOFEqU7JPuvKD0QPAM5MLuZ5KluxdgWTKBDN5U"}
[30-May-2025 16:59:33 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 16:59:33 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjQzNzIsImV4cCI6MTc0ODYyNDY3Mn0.GgqNXEOFEqU7JPuvKD0QPAM5MLuZ5KluxdgWTKBDN5U","Content-Type: application\/json"]
[30-May-2025 16:59:35 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjQzNzIsImV4cCI6MTc0ODYyNDY3Mn0.GgqNXEOFEqU7JPuvKD0QPAM5MLuZ5KluxdgWTKBDN5U","Content-Type: application\/json"]
[30-May-2025 16:59:35 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 16:59:35 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:59:36 UTC] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[30-May-2025 16:59:36 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjQzNzIsImV4cCI6MTc0ODYyNDY3Mn0.GgqNXEOFEqU7JPuvKD0QPAM5MLuZ5KluxdgWTKBDN5U","Content-Type: application\/json"]
[30-May-2025 16:59:37 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:59:37 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjQzNzIsImV4cCI6MTc0ODYyNDY3Mn0.GgqNXEOFEqU7JPuvKD0QPAM5MLuZ5KluxdgWTKBDN5U","Content-Type: application\/json"]
[30-May-2025 16:59:37 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:59:37 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 16:59:38 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 16:59:38 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"TTestAddress123456789012345678901","currency":"trx","amount":1}]}
[30-May-2025 16:59:38 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjQzNzIsImV4cCI6MTc0ODYyNDY3Mn0.GgqNXEOFEqU7JPuvKD0QPAM5MLuZ5KluxdgWTKBDN5U","Content-Type: application\/json"]
[30-May-2025 16:59:39 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 16:59:39 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TExampleAddress123456789012345678
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: **********************************
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: ******************************************
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TTestAddress123456789012345678901
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:01:34 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TExampleAddress123456789012345678
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: **********************************
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: ******************************************
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TTestAddress123456789012345678901
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:02:46 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TExampleAddress123456789012345678
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: **********************************
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: ******************************************
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TTestAddress123456789012345678901
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:17:22 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: TExampleAddress123456789012345678
[30-May-2025 17:18:03 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 17:18:03 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 17:18:03 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 17:18:03 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 17:18:04 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjU0ODMsImV4cCI6MTc0ODYyNTc4M30.mhnO0J1YcLZgeSdbRACXljmkEg2-Cj0K5tVsuoX0Y5o"}
[30-May-2025 17:18:04 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 17:18:04 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjU0ODMsImV4cCI6MTc0ODYyNTc4M30.mhnO0J1YcLZgeSdbRACXljmkEg2-Cj0K5tVsuoX0Y5o","Content-Type: application\/json"]
[30-May-2025 17:18:04 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjU0ODMsImV4cCI6MTc0ODYyNTc4M30.mhnO0J1YcLZgeSdbRACXljmkEg2-Cj0K5tVsuoX0Y5o","Content-Type: application\/json"]
[30-May-2025 17:18:05 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:18:05 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:18:06 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[30-May-2025 17:18:06 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjU0ODMsImV4cCI6MTc0ODYyNTc4M30.mhnO0J1YcLZgeSdbRACXljmkEg2-Cj0K5tVsuoX0Y5o","Content-Type: application\/json"]
[30-May-2025 17:18:07 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 17:18:07 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjU0ODMsImV4cCI6MTc0ODYyNTc4M30.mhnO0J1YcLZgeSdbRACXljmkEg2-Cj0K5tVsuoX0Y5o","Content-Type: application\/json"]
[30-May-2025 17:18:08 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 17:18:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:18:08 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 17:18:08 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":10}]}
[30-May-2025 17:18:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjU0ODMsImV4cCI6MTc0ODYyNTc4M30.mhnO0J1YcLZgeSdbRACXljmkEg2-Cj0K5tVsuoX0Y5o","Content-Type: application\/json"]
[30-May-2025 17:18:10 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":10,"actualBalance":1.058}}}
[30-May-2025 17:59:24 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 17:59:24 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 17:59:24 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 17:59:25 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE"}
[30-May-2025 17:59:25 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 17:59:25 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:27 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:27 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:27 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[30-May-2025 17:59:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:29 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdterc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:29 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:29 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:30 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:31 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:32 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:32 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:33 UTC] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[30-May-2025 17:59:33 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:34 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:35 UTC] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.001
[30-May-2025 17:59:35 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:36 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:37 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:38 UTC] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[30-May-2025 17:59:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bnb с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:40 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bnb с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:40 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:40 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bnb с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:43 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:44 UTC] NOWPaymentsAPI INFO: Используем известный минимум для ltc: 0.001
[30-May-2025 17:59:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:46 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:46 UTC] NOWPaymentsAPI INFO: Используем известный минимум для bch: 0.001
[30-May-2025 17:59:47 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:48 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:49 UTC] NOWPaymentsAPI INFO: Используем известный минимум для xrp: 1
[30-May-2025 17:59:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ada с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ada с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:51 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ada с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:52 UTC] NOWPaymentsAPI INFO: Используем известный минимум для ada: 1
[30-May-2025 17:59:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dot с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dot с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:53 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dot с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:54 UTC] NOWPaymentsAPI INFO: Используем известный минимум для dot: 0.1
[30-May-2025 17:59:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/doge с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/doge с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:56 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/doge с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:57 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/matic с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/matic с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 17:59:58 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 17:59:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/matic с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 17:59:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/sol с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/sol с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:01 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:00:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/sol с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:00:03 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/avax с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:04 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/avax с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:05 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:00:05 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/avax с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:00:07 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:11 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:12 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:00:12 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:00:12 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dai с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:13 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dai с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:14 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:00:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dai с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:00:15 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/link с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/link с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:16 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:00:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/link с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:00:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/uni с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/uni с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:19 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:00:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/uni с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:00:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/atom с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/atom с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2Mjc5NjQsImV4cCI6MTc0ODYyODI2NH0.on8q97V8-s55XLradoK256N3Xmv4kGx9o5fdxuTEEEE","Content-Type: application\/json"]
[30-May-2025 18:00:22 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:00:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/atom с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:16 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 18:01:16 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 18:01:16 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 18:01:17 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s"}
[30-May-2025 18:01:17 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 18:01:17 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:18 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:19 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[30-May-2025 18:01:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:20 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdterc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:20 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:20 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:23 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:23 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:24 UTC] NOWPaymentsAPI INFO: Используем известный минимум для btc: 5.0E-6
[30-May-2025 18:01:24 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:25 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:26 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:27 UTC] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.001
[30-May-2025 18:01:27 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:28 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:29 UTC] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[30-May-2025 18:01:29 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bnb с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:30 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bnb с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:31 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:31 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bnb с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:32 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:33 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:33 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:33 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ltc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:34 UTC] NOWPaymentsAPI INFO: Используем известный минимум для ltc: 0.001
[30-May-2025 18:01:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:35 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:36 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:36 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/bch с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:37 UTC] NOWPaymentsAPI INFO: Используем известный минимум для bch: 0.001
[30-May-2025 18:01:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:39 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/xrp с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:39 UTC] NOWPaymentsAPI INFO: Используем известный минимум для xrp: 1
[30-May-2025 18:01:40 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ada с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:40 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ada с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:41 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/ada с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:42 UTC] NOWPaymentsAPI INFO: Используем известный минимум для ada: 1
[30-May-2025 18:01:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dot с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dot с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:43 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dot с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:44 UTC] NOWPaymentsAPI INFO: Используем известный минимум для dot: 0.1
[30-May-2025 18:01:44 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/doge с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/doge с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:45 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/doge с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/matic с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:47 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/matic с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:48 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:48 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/matic с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:49 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/sol с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/sol с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:51 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/sol с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/avax с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/avax с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:53 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/avax с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdc с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:56 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:57 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dai с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:57 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dai с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:01:58 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:01:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/dai с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:01:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/link с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:02:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/link с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:02:00 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:02:00 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/link с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:02:02 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/uni с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:02:03 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/uni с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:02:03 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:02:03 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/uni с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:02:05 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/atom с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:02:06 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/atom с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjgwNzYsImV4cCI6MTc0ODYyODM3Nn0.V7ZfnE22PCxoLGV2H5lxEqU-0Ns94NF2Fp3RA_Nwi8s","Content-Type: application\/json"]
[30-May-2025 18:02:07 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:02:07 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/atom с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:29 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 18:21:29 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 18:21:29 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 18:21:29 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA"}
[30-May-2025 18:21:29 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 18:21:29 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:30 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:30 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:31 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:31 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:31 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:32 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:32 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:32 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:33 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:33 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:33 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:34 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:34 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:34 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:35 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:35 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:35 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:36 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:36 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:36 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:37 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:37 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:37 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:38 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:38 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:38 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:39 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:39 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:39 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:40 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:40 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:40 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:41 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:41 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:42 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:42 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:43 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:43 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:43 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:43 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:44 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:44 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:45 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:45 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:45 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:45 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:46 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:46 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:47 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:47 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkyODgsImV4cCI6MTc0ODYyOTU4OH0.XnbqlBdOzdURrNFhS6M0Qm_iUv0bMkCLg0J9cp_rpSA","Content-Type: application\/json"]
[30-May-2025 18:21:47 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:21:47 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:21:48 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[30-May-2025 18:22:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI ERROR: Попытка отправки на тестовый адрес: **********************************
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkzODAsImV4cCI6MTc0ODYyOTY4MH0.ptwZkNKTErpvNIHFKSrNatpwVPATAfQJX0a-VgMP5qc"}
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[30-May-2025 18:23:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkzODAsImV4cCI6MTc0ODYyOTY4MH0.ptwZkNKTErpvNIHFKSrNatpwVPATAfQJX0a-VgMP5qc","Content-Type: application\/json"]
[30-May-2025 18:23:03 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg2MjkzODAsImV4cCI6MTc0ODYyOTY4MH0.ptwZkNKTErpvNIHFKSrNatpwVPATAfQJX0a-VgMP5qc","Content-Type: application\/json"]
[30-May-2025 18:23:03 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[30-May-2025 18:23:03 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[30-May-2025 18:23:04 UTC] NOWPaymentsAPI INFO: Используем известный минимум для trx: 1
[01-Jun-2025 12:49:36 UTC] loadUserData INFO: Загрузка данных из D:\OSPanel\domains\argun-defolt.loc\api/user_data.json
[01-Jun-2025 12:49:36 UTC] loadUserData INFO: Данные успешно загружены.
[01-Jun-2025 15:49:36 Europe/Moscow] requestWithdrawal INFO: Получен запрос на вывод 1000 монет в eth на адрес ******************************************
[01-Jun-2025 15:49:36 Europe/Moscow] validateTelegramInitData: auth_date старый (но разрешен).
[01-Jun-2025 15:49:36 Europe/Moscow] validateTelegramInitData: Хэши НЕ совпадают. Проверьте TELEGRAM_BOT_TOKEN!
[01-Jun-2025 12:50:31 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:32 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=10&currency_from=usd&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:33 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[01-Jun-2025 12:50:33 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[01-Jun-2025 12:50:33 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[01-Jun-2025 12:50:33 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIyMzAsImV4cCI6MTc0ODc4MjUzMH0.fN0FWlpXfkks0aJmSI7YFyh6vDW2riAp_LUPjGFdegs"}
[01-Jun-2025 12:50:33 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[01-Jun-2025 12:50:33 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIyMzAsImV4cCI6MTc0ODc4MjUzMH0.fN0FWlpXfkks0aJmSI7YFyh6vDW2riAp_LUPjGFdegs","Content-Type: application\/json"]
[01-Jun-2025 12:50:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIyMzAsImV4cCI6MTc0ODc4MjUzMH0.fN0FWlpXfkks0aJmSI7YFyh6vDW2riAp_LUPjGFdegs","Content-Type: application\/json"]
[01-Jun-2025 12:50:35 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:50:35 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:36 UTC] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.001
[01-Jun-2025 12:50:36 UTC] NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией
[01-Jun-2025 12:50:36 UTC] NOWPaymentsAPI INFO: Цель - 0.00402562 eth на адрес ******************************************
[01-Jun-2025 12:50:36 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса: eth, usdterc20
[01-Jun-2025 12:50:36 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:37 UTC] NOWPaymentsAPI INFO: Поиск лучшей автоконвертации
[01-Jun-2025 12:50:37 UTC] NOWPaymentsAPI INFO: Проверяем btc: 1.0E-5
[01-Jun-2025 12:50:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402562&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:39 UTC] NOWPaymentsAPI INFO: Проверяем usdttrc20: 1.058
[01-Jun-2025 12:50:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.058&currency_from=usdttrc20&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402562&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:40 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.058&currency_from=usdttrc20&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:41 UTC] NOWPaymentsAPI INFO: Проверяем eth: 0.000119
[01-Jun-2025 12:50:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402562&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000119&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:42 UTC] NOWPaymentsAPI INFO: Проверяем trx: 0.149006
[01-Jun-2025 12:50:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402562&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:43 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:50:44 UTC] NOWPaymentsAPI ERROR: Не найдено опций автоконвертации
[01-Jun-2025 12:51:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:51:17 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:51:18 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[01-Jun-2025 12:51:18 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[01-Jun-2025 12:51:18 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[01-Jun-2025 12:51:18 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIyNzUsImV4cCI6MTc0ODc4MjU3NX0.M85BcNPZ7QxHDBozQw_a2tCTCFnLp82qrrt49EfScGI"}
[01-Jun-2025 12:51:18 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[01-Jun-2025 12:51:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIyNzUsImV4cCI6MTc0ODc4MjU3NX0.M85BcNPZ7QxHDBozQw_a2tCTCFnLp82qrrt49EfScGI","Content-Type: application\/json"]
[01-Jun-2025 12:51:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIyNzUsImV4cCI6MTc0ODc4MjU3NX0.M85BcNPZ7QxHDBozQw_a2tCTCFnLp82qrrt49EfScGI","Content-Type: application\/json"]
[01-Jun-2025 12:51:20 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:51:20 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:51:21 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[01-Jun-2025 12:52:09 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:10 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:11 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[01-Jun-2025 12:52:11 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[01-Jun-2025 12:52:11 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[01-Jun-2025 12:52:11 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzMjgsImV4cCI6MTc0ODc4MjYyOH0.rYG0VmKfnR5mNS_bTi6TovBlIdDZDCDiGNZAz3jJCCY"}
[01-Jun-2025 12:52:11 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[01-Jun-2025 12:52:11 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzMjgsImV4cCI6MTc0ODc4MjYyOH0.rYG0VmKfnR5mNS_bTi6TovBlIdDZDCDiGNZAz3jJCCY","Content-Type: application\/json"]
[01-Jun-2025 12:52:12 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzMjgsImV4cCI6MTc0ODc4MjYyOH0.rYG0VmKfnR5mNS_bTi6TovBlIdDZDCDiGNZAz3jJCCY","Content-Type: application\/json"]
[01-Jun-2025 12:52:13 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:52:13 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:14 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[01-Jun-2025 12:52:14 UTC] NOWPaymentsAPI INFO: Создание выплаты с полной автоконвертацией
[01-Jun-2025 12:52:14 UTC] NOWPaymentsAPI INFO: Цель - 0.998316 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[01-Jun-2025 12:52:14 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса: trx, usdttrc20
[01-Jun-2025 12:52:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:15 UTC] NOWPaymentsAPI INFO: Поиск лучшей автоконвертации
[01-Jun-2025 12:52:15 UTC] NOWPaymentsAPI INFO: Проверяем btc: 1.0E-5
[01-Jun-2025 12:52:15 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.998316&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:16 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.5943115849165E-6 btc -> 3.720864 trx
[01-Jun-2025 12:52:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:17 UTC] NOWPaymentsAPI INFO: Найдена опция: 9.6198194204882E-6 btc -> 0.998316 usdttrc20
[01-Jun-2025 12:52:17 UTC] NOWPaymentsAPI INFO: Проверяем usdttrc20: 1.058
[01-Jun-2025 12:52:17 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.998316&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.058&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:18 UTC] NOWPaymentsAPI INFO: Найдена опция: 0.99831591237322 usdttrc20 -> 3.720864 trx
[01-Jun-2025 12:52:18 UTC] NOWPaymentsAPI INFO: Найдена опция: 0.998316 usdttrc20 -> 0.998316 usdttrc20
[01-Jun-2025 12:52:18 UTC] NOWPaymentsAPI INFO: Проверяем eth: 0.000119
[01-Jun-2025 12:52:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.998316&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000119&currency_from=eth&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:19 UTC] NOWPaymentsAPI CURL ERROR: TLS connect error: error:00000000:lib(0)::reason(0)
[01-Jun-2025 12:52:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000119&currency_from=eth&currency_to=trx с заголовками: ["x-api-key: f6627c2b-98ac-4d30-90dc-c01324330248","Content-Type: application\/json"]
[01-Jun-2025 12:52:20 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:52:20 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000119&currency_from=eth&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:21 UTC] NOWPaymentsAPI INFO: Проверяем trx: 0.149006
[01-Jun-2025 12:52:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.998316&currency_from=usdttrc20&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:22 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция: {"source_currency":"btc","source_amount":9.619819420488162e-6,"target_currency":"usdttrc20","target_amount":"0.998316","original_target_currency":"usdttrc20","original_target_amount":"0.998316","priority":1,"efficiency":103777}
[01-Jun-2025 12:52:22 UTC] NOWPaymentsAPI INFO: Создаем выплату с автоконвертацией: {"source_currency":"btc","source_amount":9.619819420488162e-6,"target_currency":"usdttrc20","target_amount":"0.998316","original_target_currency":"usdttrc20","original_target_amount":"0.998316","priority":1,"efficiency":103777}
[01-Jun-2025 12:52:22 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[01-Jun-2025 12:52:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzMjgsImV4cCI6MTc0ODc4MjYyOH0.rYG0VmKfnR5mNS_bTi6TovBlIdDZDCDiGNZAz3jJCCY","Content-Type: application\/json"]
[01-Jun-2025 12:52:24 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzMjgsImV4cCI6MTc0ODc4MjYyOH0.rYG0VmKfnR5mNS_bTi6TovBlIdDZDCDiGNZAz3jJCCY","Content-Type: application\/json"]
[01-Jun-2025 12:52:25 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:52:25 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI ERROR: Внутренняя конвертация не удалась, пробуем внешнюю автоконвертацию
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI INFO: Цель - 0.998316 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzMjgsImV4cCI6MTc0ODc4MjYyOH0.rYG0VmKfnR5mNS_bTi6TovBlIdDZDCDiGNZAz3jJCCY","Content-Type: application\/json"]
[01-Jun-2025 12:52:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzMjgsImV4cCI6MTc0ODc4MjYyOH0.rYG0VmKfnR5mNS_bTi6TovBlIdDZDCDiGNZAz3jJCCY","Content-Type: application\/json"]
[01-Jun-2025 12:52:27 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:52:27 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:52:27 UTC] NOWPaymentsAPI INFO: Используем известный минимум для usdttrc20: 8.58
[01-Jun-2025 12:52:27 UTC] NOWPaymentsAPI ERROR: Ошибка не связана с балансом, автоконвертация невозможна
[01-Jun-2025 12:53:07 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:07 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=10&currency_from=usd&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:08 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[01-Jun-2025 12:53:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[01-Jun-2025 12:53:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[01-Jun-2025 12:53:09 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE"}
[01-Jun-2025 12:53:09 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[01-Jun-2025 12:53:09 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE","Content-Type: application\/json"]
[01-Jun-2025 12:53:10 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE","Content-Type: application\/json"]
[01-Jun-2025 12:53:10 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:53:10 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:11 UTC] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.001
[01-Jun-2025 12:53:11 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[01-Jun-2025 12:53:11 UTC] NOWPaymentsAPI INFO: Цель - 0.00402193 eth на адрес ******************************************
[01-Jun-2025 12:53:11 UTC] NOWPaymentsAPI INFO: Адрес совместим с eth, пробуем прямую выплату
[01-Jun-2025 12:53:11 UTC] NOWPaymentsAPI INFO: Создание выплаты с обработкой комиссий
[01-Jun-2025 12:53:11 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE","Content-Type: application\/json"]
[01-Jun-2025 12:53:12 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE","Content-Type: application\/json"]
[01-Jun-2025 12:53:12 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[01-Jun-2025 12:53:12 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout-withdrawal/min-amount/eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:13 UTC] NOWPaymentsAPI INFO: Используем известный минимум для eth: 0.001
[01-Jun-2025 12:53:13 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE","Content-Type: application\/json"]
[01-Jun-2025 12:53:14 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[01-Jun-2025 12:53:14 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE","Content-Type: application\/json"]
[01-Jun-2025 12:53:14 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[01-Jun-2025 12:53:14 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal/fee-estimate с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:15 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[01-Jun-2025 12:53:15 UTC] NOWPaymentsAPI INFO: Создание выплаты с комиссией за счет получателя: {"withdrawals":[{"address":"******************************************","currency":"eth","amount":0.00402193}]}
[01-Jun-2025 12:53:15 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg3ODIzODYsImV4cCI6MTc0ODc4MjY4Nn0.uQovOzodWJGelFoZpdiNOa5HHtXlQfX9c9hDd6qSoOE","Content-Type: application\/json"]
[01-Jun-2025 12:53:16 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"eth":{"requested":0.004021,"actualBalance":0.000119}}}
[01-Jun-2025 12:53:16 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[01-Jun-2025 12:53:16 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:17 UTC] NOWPaymentsAPI INFO: Совместимые валюты для адреса ******************************************: eth, usdterc20
[01-Jun-2025 12:53:17 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:18 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:20 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.058&currency_from=usdttrc20&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:21 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.058&currency_from=usdttrc20&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:23 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000119&currency_from=eth&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:23 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:24 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.000119&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:25 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:25 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:27 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.149006&currency_from=trx&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.00402193&currency_from=eth&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[01-Jun-2025 12:53:28 UTC] NOWPaymentsAPI ERROR: Не найдено валют для автоконвертации
