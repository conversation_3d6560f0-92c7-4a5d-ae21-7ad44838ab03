<?php
/**
 * api/withdrawal_callback.php
 * Обработчик IPN (Instant Payment Notification) от NOWPayments API
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in withdrawal_callback.php');
    exit;
}
if (!(@require_once __DIR__ . '/db_mock.php')) {
    http_response_code(500);
    error_log('FATAL: db_mock.php not found in withdrawal_callback.php');
    exit;
}
if (!(@require_once __DIR__ . '/NOWPaymentsAPI.php')) {
    http_response_code(500);
    error_log('FATAL: NOWPaymentsAPI.php not found in withdrawal_callback.php');
    exit;
}
// --- Конец проверки зависимостей ---

// Получаем данные IPN уведомления
$ipnData = file_get_contents('php://input');
error_log("withdrawal_callback INFO: Получено IPN уведомление: " . $ipnData);

// Проверяем наличие заголовка с подписью
if (!isset($_SERVER['HTTP_X_NOWPAYMENTS_SIG'])) {
    error_log("withdrawal_callback ERROR: Отсутствует заголовок X-NOWPayments-Sig");
    http_response_code(400);
    exit;
}

$signature = $_SERVER['HTTP_X_NOWPAYMENTS_SIG'];

try {
    // Создаем экземпляр API клиента
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

    // Проверяем подпись
    if (!$api->verifyIPNSignature($ipnData, $signature)) {
        error_log("withdrawal_callback ERROR: Неверная подпись IPN");
        http_response_code(403);
        exit;
    }

    // Декодируем данные
    $data = json_decode($ipnData, true);
    if (!$data) {
        error_log("withdrawal_callback ERROR: Не удалось декодировать JSON данные");
        http_response_code(400);
        exit;
    }

    // Проверяем наличие необходимых полей (NOWPayments использует другие поля)
    if (!isset($data['id']) || !isset($data['status'])) {
        error_log("withdrawal_callback ERROR: Отсутствуют обязательные поля в IPN данных");
        http_response_code(400);
        exit;
    }

    $withdrawalId = $data['id'];
    $status = $data['status'];
    $userId = $data['extra_id'] ?? null; // NOWPayments передает user_id в extra_id

    if (!$userId) {
        error_log("withdrawal_callback ERROR: Не найден user_id в extra_id");
        http_response_code(400);
        exit;
    }

    $userId = intval($userId);

    error_log("withdrawal_callback INFO: Обработка IPN для withdrawal_id: {$withdrawalId}, status: {$status}, user_id: {$userId}");

    // Обрабатываем статус выплаты (NOWPayments статусы)
    switch ($status) {
        case 'finished':
        case 'confirmed':
            // Выплата успешно выполнена
            recordSuccessfulWithdrawal($userId, $withdrawalId, $data);
            break;

        case 'failed':
        case 'expired':
        case 'refunded':
            // Выплата не удалась, возвращаем средства пользователю
            refundFailedWithdrawal($userId, $withdrawalId, $data);
            break;

        case 'waiting':
        case 'confirming':
        case 'sending':
        default:
            // Другие статусы (pending, processing и т.д.) просто логируем
            error_log("withdrawal_callback INFO: Получен статус '{$status}' для withdrawal_id: {$withdrawalId}");
            break;
    }

    // Отправляем успешный ответ
    http_response_code(200);
    echo 'OK';

} catch (Exception $e) {
    error_log("withdrawal_callback ERROR: Исключение при обработке IPN: " . $e->getMessage());
    http_response_code(500);
    exit;
}

/**
 * Записывает информацию об успешной выплате
 *
 * @param int $userId ID пользователя
 * @param string $withdrawalId ID выплаты
 * @param array $data Данные IPN уведомления
 */
function recordSuccessfulWithdrawal($userId, $withdrawalId, $data) {
    error_log("recordSuccessfulWithdrawal INFO: Запись успешной выплаты для пользователя {$userId}, withdrawal_id: {$withdrawalId}");

    // Здесь можно добавить код для записи информации о выплате в базу данных
    // Например, создать таблицу withdrawals и записать туда данные

    // Загружаем данные пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        error_log("recordSuccessfulWithdrawal ERROR: Пользователь {$userId} не найден");
        return;
    }

    // Добавляем информацию о выплате в данные пользователя
    if (!isset($userData[$userId]['withdrawals'])) {
        $userData[$userId]['withdrawals'] = [];
    }

    $userData[$userId]['withdrawals'][] = [
        'id' => $withdrawalId,
        'status' => 'confirmed',
        'amount' => $data['amount'] ?? 0,
        'currency' => $data['currency'] ?? '',
        'timestamp' => time()
    ];

    // Сохраняем данные пользователя
    if (!saveUserData($userData)) {
        error_log("recordSuccessfulWithdrawal ERROR: Не удалось сохранить данные пользователя {$userId}");
    }
}

/**
 * Возвращает средства пользователю при неудачной выплате
 *
 * @param int $userId ID пользователя
 * @param string $withdrawalId ID выплаты
 * @param array $data Данные IPN уведомления
 */
function refundFailedWithdrawal($userId, $withdrawalId, $data) {
    error_log("refundFailedWithdrawal INFO: Возврат средств для пользователя {$userId}, withdrawal_id: {$withdrawalId}");

    // Загружаем данные пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        error_log("refundFailedWithdrawal ERROR: Пользователь {$userId} не найден");
        return;
    }

    // Определяем сумму для возврата (в монетах)
    // Предполагаем, что у нас есть информация о сумме в монетах, которую пользователь запросил на вывод
    // Если такой информации нет, можно использовать примерную конвертацию обратно из криптовалюты
    $refundAmount = 0;

    // Если у нас есть информация о сумме в USD
    if (isset($data['fiat_amount']) && isset($data['fiat_currency']) && $data['fiat_currency'] === 'USD') {
        $usdAmount = floatval($data['fiat_amount']);
        $refundAmount = floor($usdAmount / CONVERSION_RATE); // Конвертируем обратно в монеты
    } else {
        // Если нет информации о сумме в USD, используем фиксированную сумму или другую логику
        $refundAmount = 100; // Примерная сумма для возврата
    }

    // Увеличиваем баланс пользователя
    $newBalance = increaseUserBalance($userId, $refundAmount, $userData);
    if ($newBalance === false) {
        error_log("refundFailedWithdrawal ERROR: Не удалось увеличить баланс пользователя {$userId}");
        return;
    }

    // Добавляем информацию о неудачной выплате в данные пользователя
    if (!isset($userData[$userId]['withdrawals'])) {
        $userData[$userId]['withdrawals'] = [];
    }

    $userData[$userId]['withdrawals'][] = [
        'id' => $withdrawalId,
        'status' => 'failed',
        'amount' => $data['amount'] ?? 0,
        'currency' => $data['currency'] ?? '',
        'refund_amount' => $refundAmount,
        'timestamp' => time()
    ];

    // Сохраняем данные пользователя
    if (!saveUserData($userData)) {
        error_log("refundFailedWithdrawal ERROR: Не удалось сохранить данные пользователя {$userId}");
    } else {
        error_log("refundFailedWithdrawal SUCCESS: Возвращено {$refundAmount} монет пользователю {$userId}. Новый баланс: {$newBalance}");
    }
}
?>
