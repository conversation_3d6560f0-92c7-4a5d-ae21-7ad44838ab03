// ======================================== //
// CYBERPUNK NAVIGATION FIX //
// ======================================== //

// Исправление навигации для кибер-панк дизайна
(function() {
    'use strict';
    
    console.log('🔧 Cyberpunk Navigation Fix loading...');
    
    // Ждем загрузки DOM
    function initNavigationFix() {
        // Получаем элементы
        const mainContentEl = document.getElementById("main-content");
        const earnSectionEl = document.getElementById("earn-section");
        const friendsSectionEl = document.getElementById("friends-section");
        const navHomeButton = document.getElementById("nav-home");
        const navEarnButton = document.getElementById("nav-earn");
        const navFriendsButton = document.getElementById("nav-friends");
        const headerBalanceInfoEl = document.getElementById("header-balance-info");
        
        if (!mainContentEl || !earnSectionEl || !friendsSectionEl) {
            console.warn('⚠️ Navigation elements not found, retrying...');
            setTimeout(initNavigationFix, 500);
            return;
        }
        
        console.log('✅ Navigation elements found, setting up...');
        
        // Текущая активная секция
        let currentSection = mainContentEl;
        
        // Функция для скрытия всех секций
        function hideAllSections() {
            [mainContentEl, earnSectionEl, friendsSectionEl].forEach(section => {
                section.classList.add('page-hidden');
                section.classList.remove('active-section', 'page-enter', 'page-enter-active', 'page-leave-active');
            });
        }
        
        // Функция для показа секции
        function showSection(targetSection, activeButton) {
            if (targetSection === currentSection) return;
            
            console.log(`🔄 Switching to section: ${targetSection.id}`);
            
            // Скрываем все секции
            hideAllSections();
            
            // Показываем целевую секцию
            targetSection.classList.remove('page-hidden');
            targetSection.classList.add('active-section');
            
            // Обновляем активную кнопку навигации
            [navHomeButton, navEarnButton, navFriendsButton].forEach(btn => {
                if (btn) btn.classList.remove('active');
            });
            if (activeButton) {
                activeButton.classList.add('active');
            }
            
            // Обновляем текущую секцию
            currentSection = targetSection;
            
            // Запускаем специфичные для секции действия
            if (targetSection === friendsSectionEl) {
                // Генерируем реферальную ссылку если нужно
                if (window.generateReferralLink) {
                    window.generateReferralLink();
                }
                // Загружаем статистику рефералов
                if (window.loadReferralStats) {
                    window.loadReferralStats();
                }
            }
            
            if (targetSection === earnSectionEl) {
                // Обновляем секцию вывода средств
                if (window.updateWithdrawalSection) {
                    window.updateWithdrawalSection();
                }
            }
            
            console.log(`✅ Section switched to: ${targetSection.id}`);
        }
        
        // Функции для показа конкретных секций
        function showMainContent() {
            showSection(mainContentEl, navHomeButton);
        }
        
        function showEarnSection() {
            showSection(earnSectionEl, navEarnButton);
        }
        
        function showFriendsSection() {
            showSection(friendsSectionEl, navFriendsButton);
        }
        
        // Инициализация - показываем главную секцию
        hideAllSections();
        showMainContent();
        
        // Подключаем обработчики событий
        if (navHomeButton) {
            navHomeButton.addEventListener("click", (e) => {
                e.preventDefault();
                showMainContent();
            });
        }
        
        if (navEarnButton) {
            navEarnButton.addEventListener("click", (e) => {
                e.preventDefault();
                showEarnSection();
            });
        }
        
        if (navFriendsButton) {
            navFriendsButton.addEventListener("click", (e) => {
                e.preventDefault();
                showFriendsSection();
            });
        }
        
        if (headerBalanceInfoEl) {
            headerBalanceInfoEl.addEventListener("click", (e) => {
                e.preventDefault();
                showEarnSection();
            });
        }
        
        // Экспортируем функции в глобальную область для совместимости
        window.showMainContent = showMainContent;
        window.showEarnSection = showEarnSection;
        window.showFriendsSection = showFriendsSection;
        
        console.log('🚀 Cyberpunk Navigation Fix activated!');
    }
    
    // Запускаем инициализацию
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initNavigationFix);
    } else {
        initNavigationFix();
    }
    
})();

// Дополнительные исправления для кибер-панк дизайна
document.addEventListener('DOMContentLoaded', function() {
    
    // Исправляем проблемы с отображением
    function fixDisplayIssues() {
        // Убираем конфликтующие стили
        const style = document.createElement('style');
        style.textContent = `
            /* Исправления для кибер-панк дизайна */
            .app-container {
                padding-top: 0 !important;
            }
            
            .app-main,
            .app-section {
                max-width: 100vw;
                overflow-x: hidden;
            }
            
            /* Исправляем z-index проблемы */
            .app-header {
                z-index: 1000 !important;
            }
            
            .app-nav {
                z-index: 1001 !important;
            }
            
            /* Исправляем отображение кнопок */
            .action-button {
                position: relative;
                z-index: 1;
            }
            
            /* Исправляем скролл */
            .app-main,
            .app-section {
                -webkit-overflow-scrolling: touch;
            }
            
            /* Убираем лишние отступы */
            body {
                margin: 0;
                padding: 0;
                overflow-x: hidden;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Применяем исправления
    fixDisplayIssues();
    
    // Исправляем проблемы с размерами экрана
    function handleResize() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }
    
    window.addEventListener('resize', handleResize);
    handleResize();
    
    console.log('🎨 Cyberpunk display fixes applied!');
});
