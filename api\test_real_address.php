<?php
/**
 * Тест реального адреса пользователя
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🧪 ТЕСТ РЕАЛЬНОГО АДРЕСА ПОЛЬЗОВАТЕЛЯ\n";
echo str_repeat("=", 50) . "\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Ваш реальный адрес для тестирования
$realAddress = 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK';
$currency = 'usdttrc20';
$amount = 10.0;

echo "📋 Тестируем ваш реальный адрес:\n";
echo "- Адрес: {$realAddress}\n";
echo "- Валюта: {$currency}\n";
echo "- Сумма: {$amount}\n\n";

echo "🔍 Проверяем, что адрес НЕ заблокирован...\n";

// Пробуем создать выплату
$result = $api->createPayoutWithFeeHandling($realAddress, $currency, $amount);

if (isset($result['error'])) {
    if ($result['code'] === 'TEST_ADDRESS_BLOCKED') {
        echo "❌ ОШИБКА: Ваш реальный адрес заблокирован!\n";
        echo "Сообщение: {$result['message']}\n";
        echo "\n🔧 Нужно убрать адрес из черного списка.\n";
        exit(1);
    } else {
        echo "⚠️ Адрес не заблокирован, но есть другая ошибка:\n";
        echo "Код: {$result['code']}\n";
        echo "Сообщение: {$result['message']}\n";
        
        if ($result['code'] === 'AMOUNT_TOO_LOW') {
            echo "\n✅ ЭТО НОРМАЛЬНО! Адрес работает, просто сумма меньше минимальной.\n";
            echo "Минимальная сумма: {$result['details']['minimum_amount']}\n";
            echo "Ваша сумма: {$result['details']['requested_amount']}\n";
            echo "\n🎯 РЕЗУЛЬТАТ: Ваш адрес НЕ заблокирован и работает корректно!\n";
            exit(0);
        }
    }
} else {
    echo "✅ ОТЛИЧНО! Выплата создана успешно!\n";
    echo "🆔 ID: {$result['id']}\n";
    
    if (isset($result['withdrawals'][0])) {
        $withdrawal = $result['withdrawals'][0];
        echo "📊 Статус: {$withdrawal['status']}\n";
        echo "💰 Сумма: {$withdrawal['amount']} {$withdrawal['currency']}\n";
    }
    
    echo "\n🎯 РЕЗУЛЬТАТ: Ваш адрес работает и выплата создана!\n";
    exit(0);
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🏁 Тестирование завершено\n";
?>
