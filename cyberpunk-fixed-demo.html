<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>Cyberpunk Fixed Demo - Applanza App</title>
    <link rel="stylesheet" href="cyberpunk-simple.css">
    <style>
        /* Demo-specific styles */
        .demo-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: transparent;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            text-align: center;
            font-family: 'Orbitron', monospace;
            color: var(--cyber-accent-neon);
            text-shadow: 0 0 10px var(--cyber-glow);
            margin-bottom: 30px;
            font-size: 28px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .feature-showcase {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
            border: 1px solid var(--cyber-border);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .feature-item h3 {
            color: var(--cyber-accent-neon);
            font-family: 'Orbitron', monospace;
            margin-bottom: 10px;
        }
        
        .feature-item p {
            color: var(--cyber-text-secondary);
            font-family: 'Rajdhani', sans-serif;
            line-height: 1.5;
        }
        
        /* Fixed layout for demo */
        .demo-app-container {
            position: relative;
            min-height: 100vh;
            padding-top: 75px;
            padding-bottom: 100px;
        }
        
        .demo-content {
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="demo-app-container">
        <!-- Header Demo -->
        <div class="app-header">
            <div class="user-info">
                <div class="user-avatar">
                    <img src="./images/user.svg" class="user-avatar-icon" alt="user">
                </div>
                <div class="user-name">CyberUser_2077</div>
            </div>
            <div class="balance-info">
                <span class="balance-amount">1,337</span>
                <span class="balance-currency">монет</span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="demo-content">
            <h1 class="demo-title">🚀 Cyberpunk Fixed</h1>
            
            <!-- Status Messages Demo -->
            <div class="demo-section">
                <h2>Статусные сообщения</h2>
                <div class="status-message success">
                    ✅ Награда зачислена! Баланс: 1337 монет
                </div>
                <div class="status-message error">
                    ❌ Ошибка: Недостаточно средств для вывода
                </div>
                <div class="status-message">
                    ⏳ Загрузка данных...
                </div>
            </div>

            <!-- Buttons Demo -->
            <div class="demo-section">
                <h2>Кибер-кнопки</h2>
                <div style="display: flex; flex-direction: column; gap: 15px;">
                    <button class="action-button purple-button">
                        🔗 Открыть ссылку
                    </button>
                    <button class="action-button blue-button">
                        📺 Смотреть видео
                    </button>
                    <button class="action-button orange-button">
                        📱 Открыть рекламу
                    </button>
                </div>
            </div>

            <!-- Content Blocks Demo -->
            <div class="demo-section">
                <div class="earn-block">
                    <h3>💰 Калькулятор вывода</h3>
                    <p>Курс: 1 монета = $0.001</p>
                    
                    <div class="withdrawal-form">
                        <label for="demo-amount">Сумма для вывода:</label>
                        <input type="number" id="demo-amount" placeholder="Введите количество монет" value="1000">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                            <span style="color: var(--cyber-accent-neon); font-family: 'Orbitron', monospace;">= $1.000</span>
                            <span style="color: var(--cyber-success); font-size: 12px;">✅ Достаточно средств</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Currency Tabs Demo -->
            <div class="demo-section">
                <div class="currency-tabs-container">
                    <div class="currency-tabs-header">
                        <button class="currency-tab active">
                            <span style="font-size: 20px;">⭐</span>
                            <span style="font-weight: 600;">Ethereum</span>
                            <span style="opacity: 0.8;">ETH</span>
                        </button>
                        <button class="currency-tab">
                            <span style="font-size: 20px;">₿</span>
                            <span style="font-weight: 600;">Bitcoin</span>
                            <span style="opacity: 0.8;">BTC</span>
                        </button>
                        <button class="currency-tab">
                            <span style="font-size: 20px;">💲</span>
                            <span style="font-weight: 600;">USDT</span>
                            <span style="opacity: 0.8;">TRC20</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Stats Demo -->
            <div class="demo-section">
                <div class="friends-block">
                    <h3>📊 Статистика рефералов</h3>
                    <div class="referral-stats">
                        <div class="stat-item">
                            <div class="stat-label">Всего рефералов:</div>
                            <div class="stat-value">42</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Заработано:</div>
                            <div class="stat-value">1337</div>
                        </div>
                    </div>
                    
                    <div class="referral-link-area">
                        <input type="text" value="https://t.me/applanza_bot?start=ref123456" readonly>
                        <button class="copy-button">📋</button>
                    </div>
                </div>
            </div>

            <!-- Features Showcase -->
            <div class="demo-section">
                <h2>✨ Особенности дизайна</h2>
                <div class="feature-showcase">
                    <div class="feature-item">
                        <h3>🌟 Неоновые цвета</h3>
                        <p>Яркие cyan, pink, purple акценты</p>
                    </div>
                    <div class="feature-item">
                        <h3>🎨 Градиенты</h3>
                        <p>Многослойные цветовые переходы</p>
                    </div>
                    <div class="feature-item">
                        <h3>🚀 Анимации</h3>
                        <p>Плавные переходы и hover-эффекты</p>
                    </div>
                    <div class="feature-item">
                        <h3>📱 Адаптивность</h3>
                        <p>Оптимизировано для мобильных устройств</p>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="demo-section">
                <div class="friends-block">
                    <h3>🎮 Как использовать</h3>
                    <p><strong>1.</strong> Замените <code>styles.css</code> на <code>cyberpunk-simple.css</code></p>
                    <p><strong>2.</strong> Подключите <code>cyberpunk-navigation-fix.js</code> для исправления навигации</p>
                    <p><strong>3.</strong> Наслаждайтесь стабильным кибер-панк дизайном!</p>
                    
                    <button class="action-button purple-button" onclick="showMessage()">
                        🎉 Протестировать эффекты
                    </button>
                </div>
            </div>
        </div>

        <!-- Navigation Demo -->
        <nav class="app-nav">
            <button class="nav-button active">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                <span>Главная</span>
            </button>
            <button class="nav-button">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span>Заработок</span>
            </button>
            <button class="nav-button">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01.99L12 11l-1.99-2.01A2.5 2.5 0 0 0 8 8H5.46c-.8 0-1.49.59-1.42 1.37L6.5 16H9v6h2v-6h2v6h2z"/>
                </svg>
                <span>Друзья</span>
            </button>
        </nav>
    </div>

    <script>
        function showMessage() {
            const messages = [
                'Кибер-панк дизайн активирован! 🚀',
                'Добро пожаловать в будущее! ⚡',
                'Система готова к работе! 🤖',
                'Неоновые эффекты включены! 💫'
            ];
            
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            
            // Создаем временное сообщение
            const messageEl = document.createElement('div');
            messageEl.className = 'status-message success';
            messageEl.textContent = randomMessage;
            messageEl.style.position = 'fixed';
            messageEl.style.top = '80px';
            messageEl.style.left = '15px';
            messageEl.style.right = '15px';
            messageEl.style.zIndex = '9999';
            messageEl.style.animation = 'fadeIn 0.5s ease-in-out';
            
            document.body.appendChild(messageEl);
            
            // Удаляем через 3 секунды
            setTimeout(() => {
                messageEl.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(messageEl);
                }, 300);
            }, 3000);
        }

        // Добавляем интерактивность кнопкам навигации
        document.querySelectorAll('.nav-button').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.nav-button').forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Добавляем интерактивность табам валют
        document.querySelectorAll('.currency-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.currency-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Копирование реферальной ссылки
        document.querySelector('.copy-button').addEventListener('click', function() {
            const input = document.querySelector('.referral-link-area input');
            input.select();
            document.execCommand('copy');
            showMessage();
        });

        console.log('🚀 Cyberpunk Fixed Demo loaded successfully!');
    </script>
</body>
</html>
