<?php
/**
 * Простой тест подключения к NOWPayments API
 */

require_once 'config.php';

echo "=== ТЕСТ ПОДКЛЮЧЕНИЯ К NOWPayments API ===\n\n";

echo "Конфигурация:\n";
echo "- API Key: " . substr(NOWPAYMENTS_API_KEY, 0, 10) . "...\n";
echo "- Public Key: " . substr(NOWPAYMENTS_PUBLIC_KEY, 0, 10) . "...\n";
echo "- API URL: " . NOWPAYMENTS_API_URL . "\n\n";

// Функция для выполнения HTTP запроса
function makeSimpleRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['error' => $error, 'http_code' => 0];
    }
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'data' => json_decode($response, true)
    ];
}

// 1. Тест статуса API
echo "1. Проверка статуса API:\n";
$result = makeSimpleRequest(NOWPAYMENTS_API_URL . '/status');
echo "HTTP Code: {$result['http_code']}\n";
echo "Response: {$result['response']}\n\n";

// 2. Тест с API ключом
echo "2. Проверка доступных валют с API ключом:\n";
$headers = [
    'Content-Type: application/json',
    'x-api-key: ' . NOWPAYMENTS_API_KEY
];
$result = makeSimpleRequest(NOWPAYMENTS_API_URL . '/currencies', 'GET', null, $headers);
echo "HTTP Code: {$result['http_code']}\n";
echo "Response: " . substr($result['response'], 0, 200) . "...\n\n";

// 3. Тест получения баланса
echo "3. Проверка баланса аккаунта:\n";
$result = makeSimpleRequest(NOWPAYMENTS_API_URL . '/balance', 'GET', null, $headers);
echo "HTTP Code: {$result['http_code']}\n";
echo "Response: {$result['response']}\n\n";

// 4. Тест минимальной суммы
echo "4. Проверка минимальной суммы для BTC:\n";
$result = makeSimpleRequest(NOWPAYMENTS_API_URL . '/payout-withdrawal/min-amount/btc', 'GET', null, $headers);
echo "HTTP Code: {$result['http_code']}\n";
echo "Response: {$result['response']}\n\n";

// 5. Тест создания простой выплаты
echo "5. Тест создания простой выплаты (БЕЗ реального создания):\n";
$payoutData = [
    'withdrawals' => [
        [
            'address' => '**********************************',
            'currency' => 'btc',
            'amount' => 0.0001
        ]
    ]
];

echo "Данные для отправки:\n";
echo json_encode($payoutData, JSON_PRETTY_PRINT) . "\n";
echo "Заголовки:\n";
foreach ($headers as $header) {
    echo "- {$header}\n";
}

// НЕ отправляем реальный запрос, только показываем что бы отправили
echo "\n⚠️ РЕАЛЬНЫЙ ЗАПРОС НЕ ОТПРАВЛЕН для безопасности\n";
echo "Для реального теста раскомментируйте следующие строки:\n";
echo "// \$result = makeSimpleRequest(NOWPAYMENTS_API_URL . '/payout', 'POST', \$payoutData, \$headers);\n";
echo "// echo \"HTTP Code: {\$result['http_code']}\\n\";\n";
echo "// echo \"Response: {\$result['response']}\\n\";\n\n";

echo "=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
