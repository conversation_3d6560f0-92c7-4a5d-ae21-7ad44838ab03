<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест локализации</title>
</head>
<body>
    <h1>Тест локализации</h1>
    
    <div id="earn-section">
        <h2>Вывод средств</h2>
        
        <div class="earn-block">
            <h3>💎 Ваш баланс</h3>
            <p>Первый блок</p>
        </div>
        
        <div class="earn-block">
            <h3 id="calculator-title">💰 Калькулятор вывода</h3>
            <p>Второй блок</p>
        </div>

        <div class="earn-block">
            <h3 id="withdrawal-title">💸 Вывод средств</h3>
            <p>Третий блок</p>
        </div>
        
        <div class="earn-block">
            <h3>📊 История выплат</h3>
            <p>Четвертый блок</p>
        </div>
    </div>

    <button onclick="testLocalization()">Тест локализации</button>
    <button onclick="showBlocks()">Показать блоки</button>

    <script src="js/localization.js"></script>
    <script>
        function testLocalization() {
            console.log('=== ТЕСТ ЛОКАЛИЗАЦИИ ===');
            
            if (window.appLocalization) {
                console.log('Локализация найдена');
                console.log('Текущий язык:', window.appLocalization.getCurrentLanguage());
                console.log('Переводы загружены:', window.appLocalization.isLoaded);
                console.log('Переводы:', window.appLocalization.translations);
                
                // Принудительно применяем переводы
                window.appLocalization.applyTranslations();
            } else {
                console.log('Локализация не найдена');
            }
        }
        
        function showBlocks() {
            console.log('=== БЛОКИ ===');
            const allEarnBlocks = document.querySelectorAll('#earn-section .earn-block');
            console.log('Найдено блоков earn-block:', allEarnBlocks.length);
            allEarnBlocks.forEach((block, index) => {
                const h3 = block.querySelector('h3');
                console.log(`Блок ${index + 1}:`, h3 ? h3.textContent : 'нет заголовка');
            });
            
            // Тестируем селекторы
            const calc = document.getElementById('calculator-title');
            const withdrawal = document.getElementById('withdrawal-title');

            console.log('Калькулятор (по ID):', calc ? calc.textContent : 'не найден');
            console.log('Вывод (по ID):', withdrawal ? withdrawal.textContent : 'не найден');
        }
        
        // Автоматически запускаем тест при загрузке
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('Автоматический тест локализации...');
                showBlocks();
                testLocalization();
            }, 1000);
        });
    </script>
</body>
</html>
