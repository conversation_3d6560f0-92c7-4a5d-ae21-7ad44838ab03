/**
 * localization.js
 * Система локализации для мини-приложения
 */

class AppLocalization {
    constructor() {
        this.currentLanguage = 'en';
        this.translations = {};
        this.fallbackLanguage = 'en';
        this.isLoaded = false;

        // Русскоязычные страны
        this.russianSpeakingCountries = [
            'RU', 'BY', 'KZ', 'KG', 'TJ', 'UZ',
            'AM', 'AZ', 'GE', 'MD', 'UA'
        ];

        // Определяем язык сразу
        this.detectLanguage();

        // Загружаем переводы асинхронно, не блокируя инициализацию
        this.loadTranslations().catch(error => {
            console.warn('Не удалось загрузить переводы, используем значения по умолчанию:', error);
        });
    }

    /**
     * Загружает переводы
     */
    async loadTranslations() {
        try {
            // Загружаем русский язык
            const ruResponse = await fetch('./locales/ru.json');
            if (ruResponse.ok) {
                const ruData = await ruResponse.json();
                this.translations['ru'] = ruData.app;
            }

            // Загружаем английский язык
            const enResponse = await fetch('./locales/en.json');
            if (enResponse.ok) {
                const enData = await enResponse.json();
                this.translations['en'] = enData.app;
            }

            this.isLoaded = true;

            // Применяем переводы к интерфейсу после загрузки
            this.applyTranslations();

            console.log('Переводы загружены успешно');

        } catch (error) {
            console.error('Ошибка загрузки переводов:', error);
            this.isLoaded = true; // Помечаем как загруженное, чтобы не блокировать работу
        }
    }

    /**
     * Определяет язык пользователя
     */
    detectLanguage() {
        console.log('[Localization] Определение языка пользователя...');

        // Список русскоязычных кодов языков
        const russianLanguageCodes = ['ru', 'be', 'uk', 'kk', 'ky', 'tg', 'uz', 'hy', 'az', 'ka', 'ro'];

        // По умолчанию английский
        this.currentLanguage = 'en';

        // Проверяем Telegram Web App данные
        if (window.Telegram && window.Telegram.WebApp && window.Telegram.WebApp.initDataUnsafe) {
            const user = window.Telegram.WebApp.initDataUnsafe.user;
            if (user) {
                console.log('[Localization] Данные пользователя Telegram:', user);

                // Проверяем язык интерфейса
                if (user.language_code) {
                    const langCode = user.language_code.toLowerCase();
                    console.log('[Localization] language_code из Telegram:', langCode);

                    // Если язык входит в список русскоязычных - ставим русский
                    if (russianLanguageCodes.includes(langCode)) {
                        this.currentLanguage = 'ru';
                        console.log('[Localization] Установлен русский язык по language_code');
                        return;
                    }
                }

                // Проверяем страну (если доступно)
                if (user.country_code) {
                    const countryCode = user.country_code.toUpperCase();
                    console.log('[Localization] country_code из Telegram:', countryCode);
                    if (this.russianSpeakingCountries.includes(countryCode)) {
                        this.currentLanguage = 'ru';
                        console.log('[Localization] Установлен русский язык по country_code');
                        return;
                    }
                }
            }
        }

        // Проверяем язык браузера
        const browserLang = navigator.language || navigator.userLanguage;
        if (browserLang) {
            const browserLangCode = browserLang.toLowerCase().split('-')[0];
            console.log('[Localization] Язык браузера:', browserLangCode);
            if (russianLanguageCodes.includes(browserLangCode)) {
                this.currentLanguage = 'ru';
                console.log('[Localization] Установлен русский язык по языку браузера');
                return;
            }
        }

        console.log('[Localization] Установлен английский язык по умолчанию');
    }

    /**
     * Получает перевод по ключу
     */
    get(key, params = {}) {
        // Если переводы еще не загружены, возвращаем ключ
        if (!this.isLoaded || !this.translations[this.currentLanguage]) {
            return key;
        }

        const keys = key.split('.');
        let translation = this.translations[this.currentLanguage] || {};

        // Ищем перевод по ключу
        for (const k of keys) {
            if (translation && typeof translation === 'object' && translation[k] !== undefined) {
                translation = translation[k];
            } else {
                // Пробуем fallback язык
                translation = this.translations[this.fallbackLanguage] || {};
                for (const fallbackKey of keys) {
                    if (translation && typeof translation === 'object' && translation[fallbackKey] !== undefined) {
                        translation = translation[fallbackKey];
                    } else {
                        return key; // Возвращаем ключ, если перевод не найден
                    }
                }
                break;
            }
        }

        // Если это не строка, возвращаем ключ
        if (typeof translation !== 'string') {
            return key;
        }

        // Заменяем параметры в строке
        for (const [param, value] of Object.entries(params)) {
            translation = translation.replace(new RegExp(`{${param}}`, 'g'), value);
        }

        return translation;
    }

    /**
     * Применяет переводы к элементам интерфейса
     */
    applyTranslations() {
        console.log(`[Localization] Применение переводов для языка: ${this.currentLanguage}`);

        if (!this.isLoaded || !this.translations[this.currentLanguage]) {
            console.warn('[Localization] Переводы еще не загружены, пропускаем применение');
            return;
        }

        // === НАВИГАЦИЯ ===
        // Главная
        const navHome = document.querySelector('#nav-home .nav-text');
        if (navHome) navHome.textContent = this.get('nav.home');

        // Заработок
        const navEarn = document.querySelector('#nav-earn .nav-text');
        if (navEarn) navEarn.textContent = this.get('nav.earnings');

        // Друзья
        const navFriends = document.querySelector('#nav-friends .nav-text');
        if (navFriends) navFriends.textContent = this.get('nav.friends');

        // === ШАПКА ===
        // Валюта баланса
        const balanceCurrency = document.querySelector('.balance-currency');
        if (balanceCurrency) balanceCurrency.textContent = this.get('currency.coins');

        // === ГЛАВНАЯ СТРАНИЦА (ЗАДАНИЯ) ===
        const tasksTitle = document.querySelector('#main-content h2');
        if (tasksTitle) tasksTitle.textContent = this.get('tasks.title');

        // Кнопки заданий
        const openLinkBtn = document.getElementById('openLinkButton');
        if (openLinkBtn) openLinkBtn.textContent = this.get('tasks.open_link');

        const watchVideoBtn = document.getElementById('watchVideoButton');
        if (watchVideoBtn) watchVideoBtn.textContent = this.get('tasks.watch_video');

        const openAdBtn = document.getElementById('openAdButton');
        if (openAdBtn) openAdBtn.textContent = this.get('tasks.watch_ad');

        // === СЕКЦИЯ ЗАРАБОТКА ===
        const earnTitle = document.querySelector('#earn-section h2');
        if (earnTitle) earnTitle.textContent = this.get('earnings.title');

        // Блок баланса
        const balanceBlockTitle = document.querySelector('#earn-section .earn-block:first-child h3');
        if (balanceBlockTitle) balanceBlockTitle.textContent = this.get('earnings.your_balance');

        const earnBalanceCurrency = document.querySelector('#earn-section .balance-currency');
        if (earnBalanceCurrency) earnBalanceCurrency.textContent = this.get('currency.coins');

        const availableText = document.querySelector('#earn-section .hint');
        if (availableText) {
            const availableAmount = document.getElementById('available-withdrawal')?.textContent || '0';
            availableText.innerHTML = this.get('earnings.available_for_withdrawal', {amount: availableAmount});
        }

        // Калькулятор вывода
        const calculatorBlockTitle = document.querySelector('#earn-section .earn-block:nth-child(2) h3');
        if (calculatorBlockTitle) calculatorBlockTitle.textContent = this.get('earnings.calculator_title');

        // Заголовок калькулятора
        const calculatorSubtitle = document.querySelector('.calculator-subtitle');
        if (calculatorSubtitle) calculatorSubtitle.textContent = this.get('earnings.exchange_rate');

        const balanceLabel = document.querySelector('.balance-label');
        if (balanceLabel) balanceLabel.textContent = this.get('earnings.your_balance') + ':';

        // Поле ввода суммы
        const calcAmountLabel = document.querySelector('label[for="calc-amount"]');
        if (calcAmountLabel) calcAmountLabel.textContent = this.get('earnings.amount_to_withdraw') + ':';

        const calcAmountInput = document.getElementById('calc-amount');
        if (calcAmountInput) calcAmountInput.placeholder = this.get('earnings.enter_coins_amount');

        const inputSuffix = document.querySelector('.input-suffix');
        if (inputSuffix) inputSuffix.textContent = this.get('currency.coins');

        // Табы валют
        const ethTabName = document.querySelector('[data-currency="eth"] .tab-name');
        if (ethTabName) ethTabName.textContent = this.get('currencies.ethereum');

        const btcTabName = document.querySelector('[data-currency="btc"] .tab-name');
        if (btcTabName) btcTabName.textContent = this.get('currencies.bitcoin');

        const usdtTabName = document.querySelector('[data-currency="usdttrc20"] .tab-name');
        if (usdtTabName) usdtTabName.textContent = this.get('currencies.usdt');

        const trxTabName = document.querySelector('[data-currency="trx"] .tab-name');
        if (trxTabName) trxTabName.textContent = this.get('currencies.tron');

        // Информация о валюте
        const requirementLabels = document.querySelectorAll('.requirement-label');
        if (requirementLabels.length >= 2) {
            requirementLabels[0].textContent = this.get('earnings.minimum') + ':';
            requirementLabels[1].textContent = this.get('earnings.network_fee') + ':';
        }

        // Результаты расчетов
        const calcLabels = document.querySelectorAll('.calc-label');
        if (calcLabels.length >= 3) {
            calcLabels[0].textContent = this.get('earnings.withdrawal_amount') + ':';
            calcLabels[1].textContent = this.get('earnings.network_fee') + ':';
            calcLabels[2].textContent = this.get('earnings.you_will_receive') + ':';
        }

        const efficiencyLabel = document.querySelector('.efficiency-label');
        if (efficiencyLabel) efficiencyLabel.textContent = this.get('earnings.efficiency') + ':';

        // Форма вывода
        const withdrawalBlockTitle = document.getElementById('withdrawal-title');
        console.log('[Localization] Поиск формы вывода по ID "withdrawal-title"');
        console.log('[Localization] Найден элемент формы вывода:', withdrawalBlockTitle);
        if (withdrawalBlockTitle) {
            const withdrawalTitle = this.get('earnings.withdrawal_request');
            console.log('[Localization] Устанавливаем заголовок формы вывода:', withdrawalTitle);
            console.log('[Localization] Текущий текст элемента:', withdrawalBlockTitle.textContent);
            withdrawalBlockTitle.textContent = withdrawalTitle;
            console.log('[Localization] Новый текст элемента:', withdrawalBlockTitle.textContent);
        } else {
            console.warn('[Localization] Элемент формы вывода не найден!');
        }

        const withdrawalHint = document.querySelector('#earn-section .earn-block:nth-child(3) .hint');
        if (withdrawalHint) withdrawalHint.textContent = this.get('earnings.withdrawal_hint');

        const withdrawalAmountLabel = document.querySelector('label[for="withdrawal-amount"]');
        if (withdrawalAmountLabel) {
            const helpIcon = withdrawalAmountLabel.querySelector('.help-tooltip');
            withdrawalAmountLabel.childNodes[0].textContent = this.get('earnings.withdrawal_amount') + ':';
        }

        const tooltipText = document.querySelector('.tooltip-text');
        if (tooltipText) tooltipText.textContent = this.get('earnings.withdrawal_amount_help');

        const withdrawalAmountInput = document.getElementById('withdrawal-amount');
        if (withdrawalAmountInput) withdrawalAmountInput.placeholder = this.get('earnings.enter_amount');

        const cryptoCurrencyLabel = document.querySelector('label[for="crypto-currency"]');
        if (cryptoCurrencyLabel) cryptoCurrencyLabel.textContent = this.get('earnings.choose_crypto') + ':';

        const cryptoAmountLabel = document.querySelector('label[for="crypto-amount"]');
        if (cryptoAmountLabel) cryptoAmountLabel.textContent = this.get('earnings.crypto_amount') + ':';

        const cryptoAmountInput = document.getElementById('crypto-amount');
        if (cryptoAmountInput) cryptoAmountInput.placeholder = this.get('earnings.crypto_amount_placeholder');

        const withdrawalAddressLabel = document.querySelector('label[for="withdrawal-address"]');
        if (withdrawalAddressLabel) withdrawalAddressLabel.textContent = this.get('earnings.wallet_address') + ':';

        const withdrawalAddressInput = document.getElementById('withdrawal-address');
        if (withdrawalAddressInput) withdrawalAddressInput.placeholder = this.get('earnings.enter_wallet_address');

        const withdrawBtn = document.getElementById('request-withdrawal-button');
        if (withdrawBtn) withdrawBtn.textContent = this.get('earnings.request_withdrawal');

        const importantHint = document.querySelector('#earn-section .earn-block:nth-child(3) .hint:last-child');
        if (importantHint) importantHint.innerHTML = `<strong>${this.get('common.important')}:</strong> ${this.get('earnings.wallet_warning')}`;

        // История выплат
        const historyTitle = document.querySelector('#earn-section .earn-block:nth-child(4) h3');
        if (historyTitle) historyTitle.textContent = this.get('earnings.withdrawal_history');

        // === СЕКЦИЯ ДРУЗЕЙ ===
        const friendsTitle = document.querySelector('#friends-section h2');
        if (friendsTitle) friendsTitle.textContent = this.get('friends.title');

        // Блок "Поделиться"
        const shareBlockTitle = document.querySelector('#friends-section .friends-block:first-child h3');
        if (shareBlockTitle) shareBlockTitle.textContent = this.get('friends.share_app');

        const shareText = document.querySelector('#friends-section .friends-block:first-child p');
        if (shareText) shareText.textContent = this.get('friends.share_description');

        const shareBtn = document.getElementById('share-app-button');
        if (shareBtn) shareBtn.textContent = this.get('friends.share_button');

        // Блок реферальной ссылки
        const referralBlockTitle = document.querySelector('#friends-section .friends-block:nth-child(2) h3');
        if (referralBlockTitle) referralBlockTitle.textContent = this.get('friends.invite_friend');

        const referralDescription = document.querySelector('#friends-section .friends-block:nth-child(2) p');
        if (referralDescription) referralDescription.textContent = this.get('friends.referral_description');

        // Статистика рефералов
        const statsTitle = document.querySelector('#friends-section .friends-block:nth-child(3) h3');
        if (statsTitle) statsTitle.textContent = this.get('friends.referral_stats');

        const totalReferralsLabel = document.querySelector('.stat-item:first-child .stat-label');
        if (totalReferralsLabel) totalReferralsLabel.textContent = this.get('friends.total_referrals') + ':';

        const referralEarningsLabel = document.querySelector('.stat-item:nth-child(2) .stat-label');
        if (referralEarningsLabel) referralEarningsLabel.textContent = this.get('friends.earned_from_referrals') + ':';

        const refreshStatsBtn = document.getElementById('refresh-stats-button');
        if (refreshStatsBtn) refreshStatsBtn.textContent = this.get('friends.refresh_stats');

        // Подписки
        const subscriptionsTitle = document.querySelector('#friends-section .friends-block:nth-child(4) h3');
        if (subscriptionsTitle) subscriptionsTitle.textContent = this.get('friends.subscriptions');

        console.log(`[Localization] Переводы применены для языка: ${this.currentLanguage}`);
    }

    /**
     * Получает текущий язык
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * Устанавливает язык
     */
    setLanguage(language) {
        console.log(`[Localization] Попытка установить язык: ${language}`);

        if (this.translations[language] || language === 'en' || language === 'ru') {
            this.currentLanguage = language;
            console.log(`[Localization] Язык установлен: ${language}`);

            // Применяем переводы только если они загружены
            if (this.isLoaded) {
                this.applyTranslations();
            } else {
                console.log(`[Localization] Переводы еще не загружены, применение отложено`);
            }
        } else {
            console.warn(`[Localization] Неподдерживаемый язык: ${language}`);
        }
    }
}

// Создаем глобальный экземпляр
window.appLocalization = new AppLocalization();
