/* ======================================== */
/* CYBERPUNK TELEGRAM MINI APP - FUTURISTIC STYLE */
/* ======================================== */

/* Import futuristic font */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* Import additional animations */
@import url('cyberpunk-animations.css');

/* --- CYBERPUNK COLOR PALETTE & VARIABLES --- */
:root {
  /* Cyberpunk Color Scheme */
  --cyber-bg-primary: #0a0a0f;
  --cyber-bg-secondary: #1a1a2e;
  --cyber-bg-tertiary: #16213e;
  --cyber-accent-neon: #00ffff;
  --cyber-accent-pink: #ff0080;
  --cyber-accent-purple: #8a2be2;
  --cyber-accent-green: #39ff14;
  --cyber-accent-orange: #ff6600;
  --cyber-text-primary: #ffffff;
  --cyber-text-secondary: #b0b0b0;
  --cyber-text-muted: #666666;
  --cyber-border: #333366;
  --cyber-glow: #00ffff;
  --cyber-error: #ff073a;
  --cyber-success: #39ff14;
  --cyber-warning: #ffaa00;

  /* Legacy compatibility */
  --app-bg-color: var(--cyber-bg-primary);
  --app-secondary-bg-color: var(--cyber-bg-secondary);
  --app-text-color: var(--cyber-text-primary);
  --app-hint-color: var(--cyber-text-secondary);
  --app-primary-color: var(--cyber-accent-neon);
  --app-primary-text-color: var(--cyber-bg-primary);
  --app-secondary-button-bg: var(--cyber-accent-pink);
  --app-secondary-button-text: var(--cyber-text-primary);
  --app-destructive-color: var(--cyber-error);
  --app-separator-color: var(--cyber-border);

  /* Button colors */
  --purple-color: var(--cyber-accent-purple);
  --orange-color: var(--cyber-accent-orange);
  --blue-color: var(--cyber-accent-neon);
  --red-color: var(--cyber-error);
  --yellow-color: var(--cyber-warning);
  --black-color: var(--cyber-bg-primary);
  --page-transition-duration: 0.4s;

  /* Sprite settings */
  --sprite-url: "images/sprite.svg";
  --icon-width: 32px;
  --icon-height: 32px;
  --sprite-total-width: calc(var(--icon-width) * 3);
  --sprite-total-height: calc(var(--icon-height) * 3);
}

/* --- CYBERPUNK ANIMATIONS --- */
@keyframes neonGlow {
  0%, 100% { 
    text-shadow: 0 0 5px var(--cyber-glow), 0 0 10px var(--cyber-glow), 0 0 15px var(--cyber-glow);
    box-shadow: 0 0 5px var(--cyber-glow);
  }
  50% { 
    text-shadow: 0 0 10px var(--cyber-glow), 0 0 20px var(--cyber-glow), 0 0 30px var(--cyber-glow);
    box-shadow: 0 0 10px var(--cyber-glow), 0 0 20px var(--cyber-glow);
  }
}

@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes slideInCyber {
  from { 
    opacity: 0; 
    transform: translateX(30px) scale(0.95);
  }
  to { 
    opacity: 1; 
    transform: translateX(0) scale(1);
  }
}

@keyframes fadeInCyber {
  from { 
    opacity: 0; 
    transform: translateY(20px);
  }
  to { 
    opacity: 1; 
    transform: translateY(0);
  }
}

/* --- GLOBAL STYLES --- */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Rajdhani', 'Orbitron', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: var(--cyber-text-primary);
  background: linear-gradient(135deg, var(--cyber-bg-primary) 0%, var(--cyber-bg-secondary) 50%, var(--cyber-bg-tertiary) 100%);
  background-attachment: fixed;
  overscroll-behavior: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  position: relative;
}

/* Cyberpunk grid background */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: -2;
  animation: pulse 4s ease-in-out infinite;
}

/* Animated particles */
body::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, var(--cyber-accent-neon), transparent),
    radial-gradient(2px 2px at 40px 70px, var(--cyber-accent-pink), transparent),
    radial-gradient(1px 1px at 90px 40px, var(--cyber-accent-green), transparent),
    radial-gradient(1px 1px at 130px 80px, var(--cyber-accent-purple), transparent);
  background-repeat: repeat;
  background-size: 200px 200px;
  animation: pulse 6s ease-in-out infinite;
  opacity: 0.1;
  z-index: -1;
}

/* --- MAIN CONTAINER --- */
.app-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 0 15px 75px 15px;
  position: relative;
  overflow-x: hidden;
  z-index: 1;
}

/* Cyberpunk border effect */
.app-container::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  border: 2px solid transparent;
  background: linear-gradient(45deg, var(--cyber-accent-neon), var(--cyber-accent-pink), var(--cyber-accent-purple)) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: exclude;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  border-radius: 15px;
  opacity: 0.3;
  z-index: -1;
}

/* --- HEADER STYLES --- */
.app-header {
  width: 100%;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.98) 0%, rgba(22, 33, 62, 0.98) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid var(--cyber-border);
  border-radius: 0px 0px 15px 15px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(0, 255, 255, 0.15);
  height: 60px;
}

.app-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--cyber-accent-neon), var(--cyber-accent-pink), var(--cyber-accent-purple));
  animation: neonGlow 2s ease-in-out infinite;
}

.user-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
  overflow: hidden;
  border: 2px solid var(--cyber-accent-neon);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  animation: neonGlow 3s ease-in-out infinite;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--cyber-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100vw - 200px);
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.balance-info {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--cyber-accent-pink), var(--cyber-accent-purple));
  padding: 6px 12px;
  border-radius: 15px;
  flex-shrink: 0;
  border: 1px solid var(--cyber-border);
  box-shadow:
    0 0 8px rgba(255, 0, 128, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  min-width: 80px;
  max-width: 120px;
  height: 32px;
  justify-content: center;
}

.balance-info:hover {
  transform: scale(1.05);
  box-shadow: 
    0 0 20px rgba(255, 0, 128, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.balance-amount {
  font-size: 14px;
  font-weight: bold;
  margin-right: 3px;
  color: var(--cyber-text-primary);
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.balance-currency {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  white-space: nowrap;
}

/* --- SECTIONS & PAGES --- */
.app-main,
.app-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 75px;
  padding: 15px;
  opacity: 1;
  transform: translateX(0);
  transition: opacity var(--page-transition-duration) ease-in-out,
    transform var(--page-transition-duration) ease-in-out;
  will-change: opacity, transform;
  background-color: transparent;
  z-index: 1;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}

.app-main.active-section,
.app-section.active-section {
  z-index: 2;
}

.page-hidden {
  display: none !important;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
  z-index: 2;
  display: flex !important;
  visibility: visible;
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  z-index: 2;
  display: flex !important;
  visibility: visible;
}

.page-leave-active {
  opacity: 0;
  transform: translateX(-20px);
  z-index: 1;
  pointer-events: none;
  display: flex !important;
}

.app-main h2,
.app-section h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--cyber-text-primary);
  font-weight: 700;
  font-size: 24px;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 0 0 10px var(--cyber-glow);
  animation: neonGlow 3s ease-in-out infinite;
  position: relative;
}

.app-main h2::after,
.app-section h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--cyber-accent-neon), transparent);
  animation: pulse 2s ease-in-out infinite;
}

/* --- STATUS MESSAGE --- */
.status-message {
  padding: 12px 15px;
  font-size: 14px;
  text-align: center;
  min-height: 20px;
  border-radius: 10px;
  word-wrap: break-word;
  margin-bottom: 15px;
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
  color: var(--cyber-text-secondary);
  border: 1px solid var(--cyber-border);
  transition: all 0.3s ease;
  opacity: 1;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  backdrop-filter: blur(5px);
}

.status-message:empty {
  opacity: 0;
  padding: 0;
  min-height: 0;
  margin-bottom: 0;
}

.status-message.success {
  color: var(--cyber-success);
  background: linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(57, 255, 20, 0.05));
  border-color: var(--cyber-success);
  box-shadow: 0 0 15px rgba(57, 255, 20, 0.3);
  animation: neonGlow 2s ease-in-out infinite;
}

.status-message.error {
  color: var(--cyber-error);
  background: linear-gradient(135deg, rgba(255, 7, 58, 0.1), rgba(255, 7, 58, 0.05));
  border-color: var(--cyber-error);
  box-shadow: 0 0 15px rgba(255, 7, 58, 0.3);
  animation: glitch 0.5s ease-in-out infinite;
}

/* --- CYBERPUNK BUTTONS --- */
.action-button {
  width: 100%;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  appearance: none;
  -webkit-appearance: none;
  text-align: center;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
  color: var(--cyber-bg-primary);
  border: 2px solid transparent;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(0, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-button:active {
  transform: translateY(0) scale(0.98);
  box-shadow:
    0 2px 10px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(0, 255, 255, 0.4);
}

/* Button color variants */
.action-button.purple-button {
  background: linear-gradient(135deg, var(--cyber-accent-purple), var(--cyber-accent-pink));
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(138, 43, 226, 0.4);
}

.action-button.purple-button:hover {
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(138, 43, 226, 0.6);
}

.action-button.orange-button {
  background: linear-gradient(135deg, var(--cyber-accent-orange), var(--cyber-warning));
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(255, 102, 0, 0.4);
}

.action-button.orange-button:hover {
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(255, 102, 0, 0.6);
}

.action-button.blue-button {
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(0, 255, 255, 0.4);
}

.action-button.blue-button:hover {
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(0, 255, 255, 0.6);
}

.action-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: linear-gradient(135deg, var(--cyber-text-muted), var(--cyber-bg-tertiary));
  color: var(--cyber-text-muted);
  box-shadow: none;
  transform: none;
}

.action-button:disabled::before {
  display: none;
}

/* Pressed effect */
.action-button.pressed {
  transform: translateY(2px) scale(0.98);
  box-shadow:
    0 1px 5px rgba(0, 0, 0, 0.4),
    inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Countdown overlay */
.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(26, 26, 46, 0.8));
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: var(--cyber-accent-neon);
  border-radius: 12px;
  z-index: 10;
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 10px var(--cyber-glow);
  animation: pulse 1s ease-in-out infinite;
}

/* --- NAVIGATION --- */
.app-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.98), rgba(22, 33, 62, 0.98));
  backdrop-filter: blur(20px) saturate(180%);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0 max(8px, env(safe-area-inset-bottom)) 0;
  border-radius: 20px 20px 0 0;
  border-top: 2px solid var(--cyber-border);
  z-index: 100;
  height: 75px;
  box-shadow:
    0 -4px 15px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(0, 255, 255, 0.15);
}

.app-nav::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--cyber-accent-neon), var(--cyber-accent-pink), var(--cyber-accent-purple));
  animation: pulse 3s ease-in-out infinite;
}

.nav-button {
  background: none;
  border: none;
  color: var(--cyber-text-muted);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  font-size: 11px;
  flex-grow: 1;
  transition: all 0.3s ease;
  border-radius: 12px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-button:hover {
  color: var(--cyber-text-secondary);
  background: rgba(0, 255, 255, 0.05);
  transform: translateY(-2px);
}

.nav-button.active {
  color: var(--cyber-accent-neon);
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(138, 43, 226, 0.1));
  border: 1px solid rgba(0, 255, 255, 0.3);
  box-shadow:
    0 0 15px rgba(0, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-shadow: 0 0 5px var(--cyber-glow);
}

.nav-button .nav-icon,
.nav-button .cyber-icon {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
  filter: brightness(0) invert(0.4);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.nav-button.active .nav-icon {
  filter: brightness(0) saturate(100%) invert(69%) sepia(68%) saturate(497%) hue-rotate(83deg) brightness(99%) contrast(93%);
  animation: neonGlow 2s ease-in-out infinite;
}

/* Cyberpunk SVG Icons */
.nav-button .cyber-icon {
  fill: var(--cyber-text-muted);
  stroke: var(--cyber-text-muted);
  stroke-width: 1.5;
  filter: none;
}

.nav-button:hover .cyber-icon {
  fill: var(--cyber-text-secondary);
  stroke: var(--cyber-text-secondary);
  filter: drop-shadow(0 0 3px rgba(0, 255, 255, 0.3));
}

.nav-button.active .cyber-icon {
  fill: var(--cyber-accent-neon);
  stroke: var(--cyber-accent-neon);
  filter: drop-shadow(0 0 5px rgba(0, 255, 255, 0.6));
  animation: neonGlow 2s ease-in-out infinite;
}

.nav-button:active {
  transform: translateY(0) scale(0.95);
}

/* --- CONTENT BLOCKS --- */
.friends-block,
.earn-block {
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
  backdrop-filter: blur(10px);
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 20px;
  border: 1px solid var(--cyber-border);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(0, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  animation: fadeInCyber 0.6s ease-out;
}

.friends-block::before,
.earn-block::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--cyber-accent-neon), var(--cyber-accent-pink), var(--cyber-accent-purple));
  opacity: 0.6;
}

.friends-block h3,
.earn-block h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--cyber-text-primary);
  font-size: 18px;
  font-weight: 600;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.friends-block p,
.earn-block p {
  font-size: 14px;
  color: var(--cyber-text-secondary);
  margin-top: 0;
  margin-bottom: 15px;
  line-height: 1.5;
  font-family: 'Rajdhani', sans-serif;
}

.friends-block p.hint,
.earn-block p.hint {
  font-size: 13px;
  font-style: italic;
  color: var(--cyber-text-muted);
  opacity: 0.8;
}

/* --- FORMS --- */
.withdrawal-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.withdrawal-form label {
  font-size: 14px;
  color: var(--cyber-text-primary);
  margin-bottom: -10px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.withdrawal-form input[type="number"],
.withdrawal-form input[type="text"],
.withdrawal-form select {
  padding: 15px 18px;
  border: 2px solid var(--cyber-border);
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  color: var(--cyber-text-primary);
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
  font-family: 'Rajdhani', sans-serif;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.withdrawal-form input:focus,
.withdrawal-form select:focus {
  outline: none;
  border-color: var(--cyber-accent-neon);
  box-shadow:
    0 0 15px rgba(0, 255, 255, 0.3),
    inset 0 2px 5px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
}

.withdrawal-form select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2300FFFF%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 15px top 50%;
  background-size: 12px auto;
  padding-right: 40px;
}

/* --- REFERRAL LINK AREA --- */
.referral-link-area {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
}

.referral-link-area input[type="text"] {
  flex-grow: 1;
  padding: 12px 15px;
  border: 2px solid var(--cyber-border);
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  color: var(--cyber-text-primary);
  border-radius: 12px;
  font-size: 14px;
  font-family: 'Rajdhani', monospace;
  transition: all 0.3s ease;
}

.referral-link-area .copy-button {
  padding: 0;
  background: linear-gradient(135deg, var(--cyber-accent-pink), var(--cyber-accent-purple));
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  flex-shrink: 0;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(255, 0, 128, 0.3);
}

.referral-link-area .copy-button:hover {
  transform: scale(1.05);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.3),
    0 0 25px rgba(255, 0, 128, 0.5);
}

/* --- CALCULATOR STYLES --- */
.calculator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(138, 43, 226, 0.1));
  border-radius: 15px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.calculator-subtitle {
  margin: 0;
  color: var(--cyber-text-secondary);
  font-size: 14px;
  font-family: 'Rajdhani', sans-serif;
}

.balance-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.balance-label {
  color: var(--cyber-text-secondary);
  font-size: 14px;
  font-family: 'Rajdhani', sans-serif;
}

.balance-amount {
  color: var(--cyber-accent-neon);
  font-weight: 600;
  font-size: 16px;
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.amount-input-section {
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
  border: 2px solid var(--cyber-border);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  backdrop-filter: blur(10px);
}

.amount-input-section label {
  display: block;
  margin-bottom: 15px;
  font-weight: 600;
  color: var(--cyber-text-primary);
  font-size: 16px;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.input-group {
  position: relative;
  margin-bottom: 15px;
}

.input-group input {
  width: 100%;
  padding: 18px 90px 18px 18px;
  border: 2px solid var(--cyber-border);
  border-radius: 12px;
  font-size: 20px;
  font-weight: 600;
  box-sizing: border-box;
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  color: var(--cyber-text-primary);
  transition: all 0.3s ease;
  font-family: 'Orbitron', monospace;
}

.input-group input:focus {
  border-color: var(--cyber-accent-neon);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
  outline: none;
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
}

.input-suffix {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--cyber-text-secondary);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  font-family: 'Rajdhani', sans-serif;
}

.amount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

#dollar-equivalent {
  font-weight: 600;
  color: var(--cyber-accent-neon);
  font-size: 18px;
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.balance-status {
  padding: 6px 15px;
  border-radius: 25px;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.balance-status.sufficient {
  background: rgba(57, 255, 20, 0.2);
  color: var(--cyber-success);
  border: 1px solid var(--cyber-success);
  box-shadow: 0 0 10px rgba(57, 255, 20, 0.3);
}

.balance-status.insufficient {
  background: rgba(255, 7, 58, 0.2);
  color: var(--cyber-error);
  border: 1px solid var(--cyber-error);
  box-shadow: 0 0 10px rgba(255, 7, 58, 0.3);
}

.balance-status.neutral {
  background: rgba(176, 176, 176, 0.2);
  color: var(--cyber-text-muted);
  border: 1px solid var(--cyber-text-muted);
}

/* --- CURRENCY TABS --- */
.currency-tabs-container {
  margin-top: 25px;
}

.currency-tabs-header {
  display: flex;
  background: rgba(26, 26, 46, 0.5);
  border-radius: 15px;
  padding: 6px;
  margin-bottom: 25px;
  overflow-x: auto;
  gap: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--cyber-border);
}

.currency-tab {
  flex: 1;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 15px 10px;
  border: none;
  background: transparent;
  color: var(--cyber-text-muted);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  position: relative;
  font-family: 'Rajdhani', sans-serif;
}

.currency-tab:hover {
  background: rgba(0, 255, 255, 0.1);
  color: var(--cyber-text-secondary);
  transform: translateY(-2px);
}

.currency-tab.active {
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
  color: var(--cyber-bg-primary);
  box-shadow:
    0 4px 15px rgba(0, 255, 255, 0.3),
    0 0 20px rgba(0, 255, 255, 0.2);
  transform: translateY(-2px);
}

.tab-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.tab-name {
  font-weight: 600;
  font-size: 13px;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tab-symbol {
  font-size: 10px;
  opacity: 0.8;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* --- CURRENCY INFO CARD --- */
.currency-info-card {
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
  border: 2px solid var(--cyber-border);
  border-radius: 20px;
  padding: 25px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(0, 255, 255, 0.1);
}

.currency-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--cyber-accent-neon), var(--cyber-accent-pink), var(--cyber-accent-purple));
  animation: neonGlow 3s ease-in-out infinite;
}

.currency-title {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.currency-icon {
  font-size: 28px;
}

.currency-full-name {
  font-size: 22px;
  font-weight: 700;
  color: var(--cyber-text-primary);
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.currency-badge {
  padding: 8px 15px;
  border-radius: 25px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Rajdhani', sans-serif;
}

.currency-badge.status-best {
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
  color: var(--cyber-bg-primary);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

/* --- STATS & LISTS --- */
.referral-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  flex: 1;
  min-width: 120px;
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  border: 1px solid var(--cyber-border);
  backdrop-filter: blur(5px);
}

.stat-label {
  font-size: 12px;
  color: var(--cyber-text-muted);
  margin-bottom: 8px;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--cyber-accent-neon);
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

.referrals-list,
.withdrawal-history {
  margin: 15px 0;
  max-height: 300px;
  overflow-y: auto;
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  border-radius: 12px;
  padding: 15px;
  border: 1px solid var(--cyber-border);
  backdrop-filter: blur(5px);
}

.withdrawal-item {
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
  border: 1px solid var(--cyber-border);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 12px;
  position: relative;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.withdrawal-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.1);
}

.withdrawal-status {
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
  font-family: 'Rajdhani', sans-serif;
  letter-spacing: 0.5px;
}

.status-pending {
  background: linear-gradient(135deg, var(--cyber-warning), #ffcc00);
  color: var(--cyber-bg-primary);
  box-shadow: 0 0 10px rgba(255, 170, 0, 0.3);
}

.status-completed {
  background: linear-gradient(135deg, var(--cyber-success), #00ff44);
  color: var(--cyber-bg-primary);
  box-shadow: 0 0 10px rgba(57, 255, 20, 0.3);
}

.status-failed {
  background: linear-gradient(135deg, var(--cyber-error), #ff4466);
  color: white;
  box-shadow: 0 0 10px rgba(255, 7, 58, 0.3);
}

/* --- ERROR MESSAGES --- */
.error-message {
  color: var(--cyber-error) !important;
  font-weight: bold !important;
  font-style: normal !important;
  text-shadow: 0 0 5px rgba(255, 7, 58, 0.5);
  animation: glitch 0.5s ease-in-out infinite;
}

/* --- MISC ELEMENTS --- */
.user-avatar-icon {
  width: 88%;
  border: 2px solid var(--cyber-accent-neon);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.clickable-balance {
  position: absolute;
  right: 9px;
  top: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clickable-balance:hover {
  transform: scale(1.05);
}

/* --- RESPONSIVE DESIGN --- */
@media (max-width: 480px) {
  .currency-tabs-header {
    flex-wrap: wrap;
  }

  .currency-tab {
    min-width: 100px;
  }

  .app-main h2,
  .app-section h2 {
    font-size: 20px;
  }

  .action-button {
    padding: 14px 20px;
    font-size: 15px;
  }
}

/* --- LEGACY COMPATIBILITY --- */
.icon {
  display: inline-block;
  width: var(--icon-width);
  height: var(--icon-height);
  background-repeat: no-repeat;
  vertical-align: middle;
  background-size: var(--sprite-total-width) var(--sprite-total-height);
}

/* Icon positions - keeping original sprite positions */
.icon-energy { background-position: 0 0; }
.icon-money { background-position: calc(var(--icon-width) * -1) 0; }
.icon-ruble { background-position: calc(var(--icon-width) * -2) 0; }
.icon-link { background-position: 0 calc(var(--icon-height) * -1); }
.icon-play { background-position: calc(var(--icon-width) * -1) calc(var(--icon-height) * -1); }
.icon-video-camera { background-position: calc(var(--icon-width) * -2) calc(var(--icon-height) * -1); }
.icon-home { background-position: 0 calc(var(--icon-height) * -2); }
.icon-dollar { background-position: calc(var(--icon-width) * -1) calc(var(--icon-height) * -2); }
.icon-friends { background-position: calc(var(--icon-width) * -2) calc(var(--icon-height) * -2); }
