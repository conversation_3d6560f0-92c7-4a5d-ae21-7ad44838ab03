<?php
/**
 * config.php
 * Конфигурация Telegram бота
 */

// Токен бота
define('BOT_TOKEN', '8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA');

// URL для webhook (замените на ваш домен)
define('WEBHOOK_URL', 'https://app.uniqpaid.com/test2/bot/webhook.php');

// URL мини-приложения
define('WEBAPP_URL', 'https://app.uniqpaid.com/test2/');

// Настройки базы данных (используем тот же файл, что и мини-приложение)
define('USER_DATA_FILE', __DIR__ . '/../api/user_data.json');

// Настройки монет
define('COINS_PER_VIEW', 10);
define('COIN_VALUE_USD', 0.001); // 1 монета = 0.001 USD

// Настройки реферальной программы
define('REFERRAL_BONUS_PERCENT', 10); // 10% от заработка рефералов

// Имя бота для ссылок
define('BOT_USERNAME', 'uniqpaid_bot'); // Имя вашего бота

// API Telegram
define('TELEGRAM_API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');

// Логирование
define('LOG_FILE', __DIR__ . '/bot.log');

/**
 * Логирование сообщений бота
 */
function botLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}\n";
    file_put_contents(LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Отправка запроса к Telegram API
 */
function telegramRequest($method, $data = []) {
    $url = TELEGRAM_API_URL . $method;

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    if ($result === false) {
        botLog("ERROR: Не удалось отправить запрос к Telegram API: {$method}");
        return false;
    }

    $response = json_decode($result, true);

    if (!$response['ok']) {
        botLog("ERROR: Telegram API вернул ошибку: " . json_encode($response));
        return false;
    }

    return $response['result'];
}

/**
 * Отправка сообщения пользователю
 */
function sendMessage($chatId, $text, $replyMarkup = null) {
    $data = [
        'chat_id' => $chatId,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = json_encode($replyMarkup);
    }

    return telegramRequest('sendMessage', $data);
}

/**
 * Отправка фото с сообщением
 */
function sendPhoto($chatId, $photo, $caption = '', $replyMarkup = null) {
    $data = [
        'chat_id' => $chatId,
        'photo' => $photo,
        'caption' => $caption,
        'parse_mode' => 'HTML'
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = json_encode($replyMarkup);
    }

    return telegramRequest('sendPhoto', $data);
}

/**
 * Установка webhook
 */
function setWebhook() {
    $data = [
        'url' => WEBHOOK_URL,
        'allowed_updates' => ['message', 'callback_query']
    ];

    return telegramRequest('setWebhook', $data);
}

/**
 * Удаление webhook
 */
function deleteWebhook() {
    return telegramRequest('deleteWebhook');
}
?>
