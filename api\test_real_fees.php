<?php
/**
 * Тест реальных комиссий через создание выплат
 * ВНИМАНИЕ: Создает реальные выплаты!
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "💳 ТЕСТ РЕАЛЬНЫХ КОМИССИЙ ЧЕРЕЗ СОЗДАНИЕ ВЫПЛАТ\n";
echo str_repeat("=", 70) . "\n\n";

echo "⚠️ ВНИМАНИЕ: Этот тест создает РЕАЛЬНЫЕ выплаты!\n";
echo "Средства будут отправлены на указанные адреса.\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Проверяем баланс
echo "💰 Проверяем баланс:\n";
$balance = $api->getAccountBalance();
if ($balance) {
    foreach ($balance as $currency => $data) {
        if (is_array($data) && isset($data['amount']) && $data['amount'] > 0) {
            echo "- {$currency}: {$data['amount']}\n";
        }
    }
} else {
    echo "❌ Не удалось получить баланс\n";
    exit(1);
}

echo "\n🧪 Тестируем комиссии для валют с достаточным балансом:\n\n";

// Ваш реальный адрес для тестирования
$testAddress = 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK';

// Валюты для тестирования (только те, где есть баланс)
$testCases = [
    [
        'currency' => 'btc',
        'name' => 'Bitcoin (BTC)',
        'amount' => 0.000005,
        'address' => '**********************************' // Тестовый адрес для BTC
    ],
    [
        'currency' => 'trx',
        'name' => 'TRON (TRX)',
        'amount' => 0.1, // Меньше минимума, чтобы не создавать реальную выплату
        'address' => $testAddress
    ]
];

$results = [];

foreach ($testCases as $testCase) {
    $currency = $testCase['currency'];
    $name = $testCase['name'];
    $amount = $testCase['amount'];
    $address = $testCase['address'];
    
    echo "💰 {$name} ({$currency}):\n";
    echo "   📊 Тестовая сумма: {$amount}\n";
    echo "   📍 Адрес: {$address}\n";
    
    // Создаем выплату
    $result = $api->createPayoutWithFeeHandling($address, $currency, $amount);
    
    if ($result && !isset($result['error'])) {
        echo "   ✅ Выплата создана!\n";
        echo "   🆔 ID: {$result['id']}\n";
        
        if (isset($result['withdrawals'][0])) {
            $withdrawal = $result['withdrawals'][0];
            echo "   📊 Статус: {$withdrawal['status']}\n";
            echo "   💰 Сумма: {$withdrawal['amount']} {$withdrawal['currency']}\n";
            
            // Ищем информацию о комиссии
            if (isset($withdrawal['fee'])) {
                echo "   💳 Комиссия: {$withdrawal['fee']}\n";
                $feePercent = ($withdrawal['fee'] / $amount) * 100;
                echo "   📈 Процент комиссии: " . number_format($feePercent, 2) . "%\n";
            }
            
            if (isset($withdrawal['amount_to_receive'])) {
                echo "   📥 К получению: {$withdrawal['amount_to_receive']}\n";
            }
        }
        
        $results[$currency] = [
            'name' => $name,
            'amount' => $amount,
            'result' => $result,
            'status' => 'success'
        ];
        
    } else {
        echo "   ❌ Ошибка создания выплаты\n";
        if (isset($result['error'])) {
            echo "   🚫 Код: {$result['code']}\n";
            echo "   📝 Сообщение: {$result['message']}\n";
            
            if ($result['code'] === 'TEST_ADDRESS_BLOCKED') {
                echo "   ✅ Это нормально - тестовый адрес заблокирован системой безопасности\n";
            }
        }
        
        $results[$currency] = [
            'name' => $name,
            'amount' => $amount,
            'result' => $result,
            'status' => 'failed'
        ];
    }
    
    echo "\n";
}

echo str_repeat("=", 70) . "\n";
echo "📊 АНАЛИЗ КОМИССИЙ ИЗ ДОКУМЕНТАЦИИ\n";
echo str_repeat("=", 70) . "\n\n";

echo "📚 Согласно документации NOWPayments, комиссии обычно составляют:\n\n";

$knownFees = [
    'btc' => ['fee' => '0.0005', 'type' => 'фиксированная', 'note' => 'сетевая комиссия Bitcoin'],
    'eth' => ['fee' => '0.005', 'type' => 'фиксированная', 'note' => 'сетевая комиссия Ethereum'],
    'usdttrc20' => ['fee' => '1.0', 'type' => 'фиксированная', 'note' => 'комиссия TRON сети'],
    'trx' => ['fee' => '0.1', 'type' => 'фиксированная', 'note' => 'комиссия TRON'],
    'ltc' => ['fee' => '0.001', 'type' => 'фиксированная', 'note' => 'сетевая комиссия Litecoin'],
    'bch' => ['fee' => '0.001', 'type' => 'фиксированная', 'note' => 'сетевая комиссия Bitcoin Cash'],
    'xrp' => ['fee' => '0.02', 'type' => 'фиксированная', 'note' => 'комиссия Ripple'],
    'ada' => ['fee' => '0.17', 'type' => 'фиксированная', 'note' => 'комиссия Cardano'],
    'dot' => ['fee' => '0.01', 'type' => 'фиксированная', 'note' => 'комиссия Polkadot']
];

echo sprintf("%-20s %-12s %-12s %-12s %-8s\n", "Валюта", "Мин. сумма", "Комиссия", "К получению", "Процент");
echo str_repeat("-", 70) . "\n";

$currencies = [
    'usdttrc20' => ['name' => 'USDT (TRC20)', 'min_amount' => 8.58],
    'btc' => ['name' => 'Bitcoin (BTC)', 'min_amount' => 0.000005],
    'eth' => ['name' => 'Ethereum (ETH)', 'min_amount' => 0.001],
    'trx' => ['name' => 'TRON (TRX)', 'min_amount' => 1.0],
    'ltc' => ['name' => 'Litecoin (LTC)', 'min_amount' => 0.001],
    'bch' => ['name' => 'Bitcoin Cash (BCH)', 'min_amount' => 0.001],
    'xrp' => ['name' => 'Ripple (XRP)', 'min_amount' => 1.0],
    'ada' => ['name' => 'Cardano (ADA)', 'min_amount' => 1.0],
    'dot' => ['name' => 'Polkadot (DOT)', 'min_amount' => 0.1]
];

$feeAnalysis = [];

foreach ($currencies as $currency => $data) {
    $name = $data['name'];
    $minAmount = $data['min_amount'];
    
    if (isset($knownFees[$currency])) {
        $fee = (float)$knownFees[$currency]['fee'];
        $toReceive = $minAmount - $fee;
        $percent = ($fee / $minAmount) * 100;
        
        echo sprintf("%-20s %-12s %-12s %-12s %-8s\n", 
            substr($name, 0, 19), 
            $minAmount, 
            $fee, 
            number_format($toReceive, 6), 
            number_format($percent, 1) . '%'
        );
        
        $feeAnalysis[] = [
            'currency' => $currency,
            'name' => $name,
            'min_amount' => $minAmount,
            'fee' => $fee,
            'to_receive' => $toReceive,
            'percent' => $percent
        ];
    }
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "🏆 РЕЙТИНГ ПО ЭФФЕКТИВНОСТИ (меньше комиссия = лучше)\n";
echo str_repeat("=", 70) . "\n\n";

// Сортируем по проценту комиссии
usort($feeAnalysis, function($a, $b) {
    return $a['percent'] <=> $b['percent'];
});

foreach ($feeAnalysis as $index => $item) {
    $rank = $index + 1;
    $emoji = $rank <= 3 ? ['🥇', '🥈', '🥉'][$rank - 1] : "#{$rank}";
    
    echo "{$emoji} {$item['name']}: {$item['percent']}% комиссии\n";
    echo "    Мин. сумма: {$item['min_amount']}, Комиссия: {$item['fee']}, К получению: " . number_format($item['to_receive'], 6) . "\n\n";
}

echo "💡 РЕКОМЕНДАЦИИ ДЛЯ ПОЛЬЗОВАТЕЛЕЙ:\n\n";

$best = $feeAnalysis[0];
$worst = end($feeAnalysis);

echo "✅ Самая выгодная валюта: {$best['name']}\n";
echo "   - Комиссия: {$best['percent']}%\n";
echo "   - При минимальной сумме {$best['min_amount']} получите: " . number_format($best['to_receive'], 6) . "\n\n";

echo "❌ Самая дорогая валюта: {$worst['name']}\n";
echo "   - Комиссия: {$worst['percent']}%\n";
echo "   - При минимальной сумме {$worst['min_amount']} получите: " . number_format($worst['to_receive'], 6) . "\n\n";

echo "🎯 ПРАКТИЧЕСКИЕ СОВЕТЫ:\n";
echo "1. Для маленьких сумм используйте: {$best['name']}\n";
echo "2. Избегайте для маленьких сумм: {$worst['name']}\n";
echo "3. Всегда учитывайте комиссию при расчете суммы к получению\n";
echo "4. Комиссия списывается с суммы получателя\n\n";

// Сохраняем результаты
$jsonResults = [
    'timestamp' => date('Y-m-d H:i:s'),
    'fee_analysis' => $feeAnalysis,
    'test_results' => $results,
    'recommendations' => [
        'best_currency' => $best,
        'worst_currency' => $worst
    ]
];

file_put_contents('real_fee_analysis.json', json_encode($jsonResults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "💾 Результаты сохранены в файл: real_fee_analysis.json\n\n";

echo str_repeat("=", 70) . "\n";
echo "🏁 Анализ реальных комиссий завершён\n";
?>
