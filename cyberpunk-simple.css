/* ======================================== */
/* CYBERPUNK SIMPLE - STABLE VERSION */
/* ======================================== */

/* Import futuristic font */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* --- CYBERPUNK COLOR PALETTE --- */
:root {
  /* Cyberpunk Color Scheme */
  --cyber-bg-primary: #0a0a0f;
  --cyber-bg-secondary: #1a1a2e;
  --cyber-bg-tertiary: #16213e;
  --cyber-accent-neon: #00ffff;
  --cyber-accent-pink: #ff0080;
  --cyber-accent-purple: #8a2be2;
  --cyber-accent-green: #39ff14;
  --cyber-accent-orange: #ff6600;
  --cyber-text-primary: #ffffff;
  --cyber-text-secondary: #b0b0b0;
  --cyber-text-muted: #666666;
  --cyber-border: #333366;
  --cyber-glow: #00ffff;
  --cyber-error: #ff073a;
  --cyber-success: #39ff14;
  --cyber-warning: #ffaa00;

  /* Legacy compatibility */
  --app-bg-color: var(--cyber-bg-primary);
  --app-secondary-bg-color: var(--cyber-bg-secondary);
  --app-text-color: var(--cyber-text-primary);
  --app-hint-color: var(--cyber-text-secondary);
  --app-primary-color: var(--cyber-accent-neon);
  --app-primary-text-color: var(--cyber-bg-primary);
  --app-secondary-button-bg: var(--cyber-accent-pink);
  --app-secondary-button-text: var(--cyber-text-primary);
  --app-destructive-color: var(--cyber-error);
  --app-separator-color: var(--cyber-border);

  /* Button colors */
  --purple-color: var(--cyber-accent-purple);
  --orange-color: var(--cyber-accent-orange);
  --blue-color: var(--cyber-accent-neon);
  --red-color: var(--cyber-error);
  --yellow-color: var(--cyber-warning);
  --black-color: var(--cyber-bg-primary);
  --page-transition-duration: 0.3s;

  /* Sprite settings */
  --sprite-url: "images/sprite.svg";
  --icon-width: 32px;
  --icon-height: 32px;
  --sprite-total-width: calc(var(--icon-width) * 3);
  --sprite-total-height: calc(var(--icon-height) * 3);
}

/* --- BASIC ANIMATIONS --- */
@keyframes neonGlow {
  0%, 100% { 
    text-shadow: 0 0 5px var(--cyber-glow);
    box-shadow: 0 0 5px var(--cyber-glow);
  }
  50% { 
    text-shadow: 0 0 10px var(--cyber-glow);
    box-shadow: 0 0 10px var(--cyber-glow);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* --- GLOBAL STYLES --- */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Rajdhani', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: var(--cyber-text-primary);
  background: linear-gradient(135deg, var(--cyber-bg-primary) 0%, var(--cyber-bg-secondary) 100%);
  background-attachment: fixed;
  overscroll-behavior: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  overflow-x: hidden;
}

/* --- MAIN CONTAINER --- */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0;
  position: relative;
  overflow-x: hidden;
  z-index: 1;
  background: linear-gradient(135deg, var(--cyber-bg-primary) 0%, var(--cyber-bg-secondary) 100%);
}

/* --- HEADER STYLES --- */
.app-header {
  width: 100%;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
  backdrop-filter: blur(10px);
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid var(--cyber-border);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  height: 65px;
}

.user-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
  overflow: hidden;
  border: 2px solid var(--cyber-accent-neon);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.user-avatar-icon {
  width: 88%;
  border-radius: 50%;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--cyber-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100vw - 200px);
  font-family: 'Orbitron', monospace;
}

.balance-info {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--cyber-accent-pink), var(--cyber-accent-purple));
  padding: 8px 15px;
  border-radius: 20px;
  flex-shrink: 0;
  border: 1px solid var(--cyber-border);
  transition: all 0.3s ease;
  cursor: pointer;
}

.balance-info:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(255, 0, 128, 0.4);
}

.balance-amount {
  font-size: 16px;
  font-weight: bold;
  margin-right: 4px;
  color: var(--cyber-text-primary);
  font-family: 'Orbitron', monospace;
}

.balance-currency {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.clickable-balance {
  position: absolute;
  right: 9px;
  top: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* --- SECTIONS & PAGES --- */
.app-main,
.app-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  position: fixed;
  top: 65px;
  left: 0;
  right: 0;
  bottom: 70px;
  padding: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
  background-color: transparent;
  z-index: 1;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
}

.app-main.active-section,
.app-section.active-section {
  z-index: 2;
}

.page-hidden {
  display: none !important;
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.app-main h2,
.app-section h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--cyber-text-primary);
  font-weight: 700;
  font-size: 24px;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 0 0 10px var(--cyber-glow);
}

/* --- STATUS MESSAGE --- */
.status-message {
  padding: 12px 15px;
  font-size: 14px;
  text-align: center;
  min-height: 20px;
  border-radius: 10px;
  word-wrap: break-word;
  margin-bottom: 15px;
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
  color: var(--cyber-text-secondary);
  border: 1px solid var(--cyber-border);
  transition: all 0.3s ease;
  opacity: 1;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
}

.status-message:empty {
  opacity: 0;
  padding: 0;
  min-height: 0;
  margin-bottom: 0;
}

.status-message.success {
  color: var(--cyber-success);
  background: linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(57, 255, 20, 0.05));
  border-color: var(--cyber-success);
  box-shadow: 0 0 15px rgba(57, 255, 20, 0.3);
}

.status-message.error {
  color: var(--cyber-error);
  background: linear-gradient(135deg, rgba(255, 7, 58, 0.1), rgba(255, 7, 58, 0.05));
  border-color: var(--cyber-error);
  box-shadow: 0 0 15px rgba(255, 7, 58, 0.3);
}

/* --- CYBERPUNK BUTTONS --- */
.action-button {
  width: 100%;
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  appearance: none;
  -webkit-appearance: none;
  text-align: center;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
  color: var(--cyber-bg-primary);
  border: 2px solid transparent;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 255, 255, 0.4);
}

.action-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Button color variants */
.action-button.purple-button {
  background: linear-gradient(135deg, var(--cyber-accent-purple), var(--cyber-accent-pink));
}

.action-button.orange-button {
  background: linear-gradient(135deg, var(--cyber-accent-orange), var(--cyber-warning));
}

.action-button.blue-button {
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
}

.action-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: linear-gradient(135deg, var(--cyber-text-muted), var(--cyber-bg-tertiary));
  color: var(--cyber-text-muted);
  box-shadow: none;
  transform: none;
}

/* --- NAVIGATION --- */
.app-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.98), rgba(22, 33, 62, 0.98));
  backdrop-filter: blur(20px);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0 max(8px, env(safe-area-inset-bottom)) 0;
  border-top: 2px solid var(--cyber-border);
  z-index: 1001;
  min-height: 70px;
  box-shadow:
    0 -4px 15px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(0, 255, 255, 0.1);
}

.nav-button {
  background: none;
  border: none;
  color: var(--cyber-text-muted);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  font-size: 11px;
  flex-grow: 1;
  transition: all 0.3s ease;
  border-radius: 12px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-button:hover {
  color: var(--cyber-text-secondary);
  background: rgba(0, 255, 255, 0.05);
  transform: translateY(-2px);
}

.nav-button.active {
  color: var(--cyber-accent-neon);
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(138, 43, 226, 0.1));
  border: 1px solid rgba(0, 255, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
  text-shadow: 0 0 5px var(--cyber-glow);
}

.nav-button .nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  filter: brightness(0) invert(0.4);
  transition: all 0.3s ease;
}

.nav-button.active .nav-icon {
  filter: brightness(0) saturate(100%) invert(69%) sepia(68%) saturate(497%) hue-rotate(83deg) brightness(99%) contrast(93%);
}

.nav-button:active {
  transform: translateY(0) scale(0.95);
}

/* --- CONTENT BLOCKS --- */
.friends-block,
.earn-block {
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 20px;
  border: 1px solid var(--cyber-border);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.friends-block h3,
.earn-block h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--cyber-text-primary);
  font-size: 18px;
  font-weight: 600;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.friends-block p,
.earn-block p {
  font-size: 14px;
  color: var(--cyber-text-secondary);
  margin-top: 0;
  margin-bottom: 15px;
  line-height: 1.5;
  font-family: 'Rajdhani', sans-serif;
}

.friends-block p.hint,
.earn-block p.hint {
  font-size: 13px;
  font-style: italic;
  color: var(--cyber-text-muted);
  opacity: 0.8;
}

/* --- FORMS --- */
.withdrawal-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.withdrawal-form label {
  font-size: 14px;
  color: var(--cyber-text-primary);
  margin-bottom: -10px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.withdrawal-form input[type="number"],
.withdrawal-form input[type="text"],
.withdrawal-form select {
  padding: 15px 18px;
  border: 2px solid var(--cyber-border);
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  color: var(--cyber-text-primary);
  border-radius: 12px;
  font-size: 16px;
  width: 100%;
  font-family: 'Rajdhani', sans-serif;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.withdrawal-form input:focus,
.withdrawal-form select:focus {
  outline: none;
  border-color: var(--cyber-accent-neon);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  background: linear-gradient(135deg, var(--cyber-bg-secondary), var(--cyber-bg-tertiary));
}

/* --- REFERRAL LINK AREA --- */
.referral-link-area {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
}

.referral-link-area input[type="text"] {
  flex-grow: 1;
  padding: 12px 15px;
  border: 2px solid var(--cyber-border);
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  color: var(--cyber-text-primary);
  border-radius: 12px;
  font-size: 14px;
  font-family: 'Rajdhani', monospace;
  transition: all 0.3s ease;
}

.referral-link-area .copy-button {
  padding: 0;
  background: linear-gradient(135deg, var(--cyber-accent-pink), var(--cyber-accent-purple));
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.referral-link-area .copy-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* --- STATS & LISTS --- */
.referral-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  flex: 1;
  min-width: 120px;
  background: linear-gradient(135deg, var(--cyber-bg-primary), var(--cyber-bg-secondary));
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  border: 1px solid var(--cyber-border);
}

.stat-label {
  font-size: 12px;
  color: var(--cyber-text-muted);
  margin-bottom: 8px;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--cyber-accent-neon);
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 5px var(--cyber-glow);
}

/* --- CURRENCY TABS --- */
.currency-tabs-container {
  margin-top: 25px;
}

.currency-tabs-header {
  display: flex;
  background: rgba(26, 26, 46, 0.5);
  border-radius: 15px;
  padding: 6px;
  margin-bottom: 25px;
  overflow-x: auto;
  gap: 4px;
  border: 1px solid var(--cyber-border);
}

.currency-tab {
  flex: 1;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 15px 10px;
  border: none;
  background: transparent;
  color: var(--cyber-text-muted);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-family: 'Rajdhani', sans-serif;
}

.currency-tab:hover {
  background: rgba(0, 255, 255, 0.1);
  color: var(--cyber-text-secondary);
  transform: translateY(-2px);
}

.currency-tab.active {
  background: linear-gradient(135deg, var(--cyber-accent-neon), var(--cyber-accent-purple));
  color: var(--cyber-bg-primary);
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* --- ERROR MESSAGES --- */
.error-message {
  color: var(--cyber-error) !important;
  font-weight: bold !important;
  font-style: normal !important;
  text-shadow: 0 0 5px rgba(255, 7, 58, 0.5);
}

/* --- LEGACY COMPATIBILITY --- */
.icon {
  display: inline-block;
  width: var(--icon-width);
  height: var(--icon-height);
  background-repeat: no-repeat;
  vertical-align: middle;
  background-size: var(--sprite-total-width) var(--sprite-total-height);
}

/* Icon positions */
.icon-energy { background-position: 0 0; }
.icon-money { background-position: calc(var(--icon-width) * -1) 0; }
.icon-ruble { background-position: calc(var(--icon-width) * -2) 0; }
.icon-link { background-position: 0 calc(var(--icon-height) * -1); }
.icon-play { background-position: calc(var(--icon-width) * -1) calc(var(--icon-height) * -1); }
.icon-video-camera { background-position: calc(var(--icon-width) * -2) calc(var(--icon-height) * -1); }
.icon-home { background-position: 0 calc(var(--icon-height) * -2); }
.icon-dollar { background-position: calc(var(--icon-width) * -1) calc(var(--icon-height) * -2); }
.icon-friends { background-position: calc(var(--icon-width) * -2) calc(var(--icon-height) * -2); }

/* --- RESPONSIVE DESIGN --- */
@media (max-width: 480px) {
  .currency-tabs-header {
    flex-wrap: wrap;
  }

  .currency-tab {
    min-width: 100px;
  }

  .app-main h2,
  .app-section h2 {
    font-size: 20px;
  }

  .action-button {
    padding: 14px 20px;
    font-size: 15px;
  }
}
